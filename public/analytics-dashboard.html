<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatAI Analytics Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-align: center;
        }

        .header p {
            color: #666;
            text-align: center;
            font-size: 1.1rem;
        }

        .controls {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }

        .control-group select,
        .control-group input {
            padding: 8px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 0.9rem;
            background: white;
            transition: border-color 0.3s;
        }

        .control-group select:focus,
        .control-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        /* Authentication Styles */
        .auth-section {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 80vh;
        }

        .auth-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 400px;
            width: 100%;
        }

        .auth-card h3 {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }

        .auth-card p {
            color: #666;
            margin-bottom: 30px;
        }

        .auth-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .auth-form input {
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
            transition: border-color 0.3s;
        }

        .auth-form input:focus {
            outline: none;
            border-color: #667eea;
        }

        .auth-form button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            transition: transform 0.2s;
        }

        .auth-form button:hover {
            transform: translateY(-2px);
        }

        .auth-message {
            margin-top: 15px;
            padding: 10px;
            border-radius: 8px;
            font-weight: 600;
        }

        .auth-message.error {
            background: #fee;
            color: #c33;
            border: 1px solid #fcc;
        }

        .auth-message.success {
            background: #efe;
            color: #3c3;
            border: 1px solid #cfc;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-healthy {
            background: #10b981;
        }

        .status-degraded {
            background: #f59e0b;
        }

        .status-critical {
            background: #ef4444;
        }

        .status-error {
            background: #6b7280;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
        }

        .metric-label {
            font-weight: 600;
            color: #333;
        }

        .metric-value {
            font-weight: 700;
            color: #667eea;
            font-size: 1.1rem;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 20px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .success {
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            color: #059669;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .performance-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .performance-item {
            background: rgba(102, 126, 234, 0.05);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #667eea;
        }

        .performance-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }

        .performance-label {
            font-size: 0.9rem;
            color: #666;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- Authentication Section -->
        <div class="auth-section" id="authSection">
            <div class="auth-card">
                <h3>🔐 Analytics Access</h3>
                <p>Enter your API key to access analytics data</p>
                <div class="auth-form">
                    <input type="password" id="apiKeyInput" placeholder="Enter API key"
                        onkeypress="handleKeyPress(event)" />
                    <button onclick="authenticate()">Login</button>
                </div>
                <div id="authMessage" class="auth-message"></div>
            </div>
        </div>

        <!-- Main Dashboard (hidden until authenticated) -->
        <div id="dashboardContent" style="display: none;">
            <div class="header">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <h1>📊 ChatAI Analytics Dashboard</h1>
                        <p>Real-time system performance and usage analytics</p>
                    </div>
                    <button class="btn" onclick="logout()" style="background: #ef4444;">🚪 Logout</button>
                </div>
            </div>

            <div class="controls">
                <div class="control-group">
                    <label for="timeRange">Time Range</label>
                    <select id="timeRange">
                        <option value="1h">Last Hour</option>
                        <option value="6h">Last 6 Hours</option>
                        <option value="24h" selected>Last 24 Hours</option>
                        <option value="7d">Last 7 Days</option>
                        <option value="30d">Last 30 Days</option>
                    </select>
                </div>

                <div class="control-group">
                    <label for="chatAiId">ChatAI ID (Optional)</label>
                    <input type="text" id="chatAiId" placeholder="Filter by specific ChatAI">
                </div>

                <div class="control-group">
                    <label for="userId">User ID (Optional)</label>
                    <input type="text" id="userId" placeholder="Filter by specific user">
                </div>

                <button class="btn" onclick="loadAnalytics()">🔄 Refresh Data</button>
                <button class="btn" onclick="exportData()">📥 Export Data</button>
            </div>

            <div id="messages"></div>

            <div class="grid">
                <!-- System Health Card -->
                <div class="card">
                    <h3>🏥 System Health</h3>
                    <div id="systemHealth">
                        <div class="loading">Loading system health...</div>
                    </div>
                </div>

                <!-- Performance Overview Card -->
                <div class="card">
                    <h3>⚡ Performance Overview</h3>
                    <div id="performanceOverview">
                        <div class="loading">Loading performance data...</div>
                    </div>
                </div>

                <!-- Usage Statistics Card -->
                <div class="card">
                    <h3>📈 Usage Statistics</h3>
                    <div id="usageStats">
                        <div class="loading">Loading usage statistics...</div>
                    </div>
                </div>

                <!-- AI Model Usage Card -->
                <div class="card">
                    <h3>🤖 AI Model Usage</h3>
                    <div id="aiModelUsage">
                        <div class="loading">Loading AI model data...</div>
                    </div>
                </div>

                <!-- Error Tracking Card -->
                <div class="card">
                    <h3>⚠️ Error Tracking</h3>
                    <div id="errorTracking">
                        <div class="loading">Loading error data...</div>
                    </div>
                </div>

                <!-- Business Metrics Card -->
                <div class="card">
                    <h3>💰 Business Metrics</h3>
                    <div id="businessMetrics">
                        <div class="loading">Loading business metrics...</div>
                    </div>
                </div>

                <!-- Performance Chart -->
                <div class="card full-width">
                    <h3>📊 Response Time Trends</h3>
                    <div class="chart-container">
                        <canvas id="performanceChart"></canvas>
                    </div>
                </div>

                <!-- Usage Chart -->
                <div class="card full-width">
                    <h3>📈 Query Volume Trends</h3>
                    <div class="chart-container">
                        <canvas id="usageChart"></canvas>
                    </div>
                </div>
            </div>
        </div> <!-- End of dashboardContent -->
    </div>

    <script>
        let performanceChart, usageChart;
        const API_BASE = window.location.origin;
        let apiKey = null;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function () {
            // Check if already authenticated
            const savedApiKey = localStorage.getItem('analyticsApiKey');
            if (savedApiKey) {
                apiKey = savedApiKey;
                authenticateWithKey(savedApiKey);
            }
        });

        // Authentication functions
        async function authenticate() {
            const apiKeyInput = document.getElementById('apiKeyInput');
            const key = apiKeyInput.value.trim();

            if (!key) {
                showAuthMessage('Please enter an API key', 'error');
                return;
            }

            await authenticateWithKey(key);
        }

        async function authenticateWithKey(key) {
            try {
                // Test the API key by making a request to analytics endpoint
                const response = await fetch(`${API_BASE}/api/v1/analytics/health?apiKey=${key}`);

                if (response.ok) {
                    // Authentication successful
                    apiKey = key;
                    localStorage.setItem('analyticsApiKey', key);
                    showAuthMessage('Authentication successful!', 'success');

                    // Hide auth section and show dashboard
                    setTimeout(() => {
                        document.getElementById('authSection').style.display = 'none';
                        document.getElementById('dashboardContent').style.display = 'block';
                        loadAnalytics();
                        // Auto-refresh every 5 minutes
                        setInterval(loadAnalytics, 5 * 60 * 1000);
                    }, 1000);
                } else {
                    const error = await response.json();
                    showAuthMessage(error.message || 'Invalid API key', 'error');
                }
            } catch (error) {
                showAuthMessage('Authentication failed: ' + error.message, 'error');
            }
        }

        function showAuthMessage(message, type) {
            const authMessage = document.getElementById('authMessage');
            authMessage.textContent = message;
            authMessage.className = `auth-message ${type}`;
        }

        function logout() {
            apiKey = null;
            localStorage.removeItem('analyticsApiKey');
            document.getElementById('authSection').style.display = 'flex';
            document.getElementById('dashboardContent').style.display = 'none';
            document.getElementById('apiKeyInput').value = '';
            document.getElementById('authMessage').textContent = '';
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                authenticate();
            }
        }

        async function loadAnalytics() {
            if (!apiKey) {
                showMessage('❌ Not authenticated. Please login first.', 'error');
                return;
            }

            const timeRange = document.getElementById('timeRange').value;
            const chatAiId = document.getElementById('chatAiId').value;
            const userId = document.getElementById('userId').value;

            showMessage('🔄 Loading analytics data...', 'info');

            try {
                // Load system analytics
                const analyticsResponse = await fetch(`${API_BASE}/api/v1/analytics/system?timeRange=${timeRange}${chatAiId ? `&chatAiId=${chatAiId}` : ''}&apiKey=${apiKey}`);
                const analytics = await analyticsResponse.json();

                if (analytics.error) {
                    throw new Error(analytics.message);
                }

                // Load system health
                const healthResponse = await fetch(`${API_BASE}/api/v1/analytics/health?apiKey=${apiKey}`);
                const health = await healthResponse.json();

                if (health.error) {
                    throw new Error(health.message);
                }

                // Update UI with data
                updateSystemHealth(health);
                updatePerformanceOverview(analytics.analytics);
                updateUsageStats(analytics.analytics);
                updateAIModelUsage(analytics.analytics);
                updateErrorTracking(analytics.analytics);
                updateBusinessMetrics(analytics.analytics);
                updateCharts(analytics.analytics);

                showMessage('✅ Analytics data loaded successfully!', 'success');

            } catch (error) {
                console.error('Error loading analytics:', error);
                showMessage(`❌ Failed to load analytics: ${error.message}`, 'error');
            }
        }

        function updateSystemHealth(health) {
            const container = document.getElementById('systemHealth');

            const statusClass = `status-${health.status}`;
            const statusText = health.status.charAt(0).toUpperCase() + health.status.slice(1);

            container.innerHTML = `
                <div class="metric">
                    <span class="metric-label">System Status</span>
                    <span class="metric-value">
                        <span class="status-indicator ${statusClass}"></span>
                        ${statusText}
                    </span>
                </div>
                <div class="metric">
                    <span class="metric-label">Last Hour Queries</span>
                    <span class="metric-value">${health.lastHour?.totalQueries || 0}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Avg Response Time</span>
                    <span class="metric-value">${Math.round(health.lastHour?.avgResponseTime || 0)}ms</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Error Rate</span>
                    <span class="metric-value">${(health.lastHour?.errorRate || 0).toFixed(2)}%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Active Sessions</span>
                    <span class="metric-value">${health.lastHour?.activeSessions || 0}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Unique Users</span>
                    <span class="metric-value">${health.lastHour?.uniqueUsers || 0}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">System Uptime</span>
                    <span class="metric-value">${formatUptime(health.system?.uptime || 0)}</span>
                </div>
            `;
        }

        function updatePerformanceOverview(analytics) {
            const container = document.getElementById('performanceOverview');
            const perf = analytics.performance || {};

            container.innerHTML = `
                <div class="performance-grid">
                    <div class="performance-item">
                        <div class="performance-value">${Math.round(perf.avgTotalDuration || 0)}ms</div>
                        <div class="performance-label">Avg Response Time</div>
                    </div>
                    <div class="performance-item">
                        <div class="performance-value">${Math.round(perf.avgVectorSearchTime || 0)}ms</div>
                        <div class="performance-label">Avg Vector Search</div>
                    </div>
                    <div class="performance-item">
                        <div class="performance-value">${Math.round(perf.avgOpenRouterTime || 0)}ms</div>
                        <div class="performance-label">Avg AI Generation</div>
                    </div>
                    <div class="performance-item">
                        <div class="performance-value">${Math.round(perf.p95TotalDuration || 0)}ms</div>
                        <div class="performance-label">95th Percentile</div>
                    </div>
                    <div class="performance-item">
                        <div class="performance-value">${Math.round(perf.fastestQuery || 0)}ms</div>
                        <div class="performance-label">Fastest Query</div>
                    </div>
                    <div class="performance-item">
                        <div class="performance-value">${Math.round(perf.slowestQuery || 0)}ms</div>
                        <div class="performance-label">Slowest Query</div>
                    </div>
                </div>
            `;
        }

        function updateUsageStats(analytics) {
            const container = document.getElementById('usageStats');
            const usage = analytics.usage || {};

            container.innerHTML = `
                <div class="metric">
                    <span class="metric-label">Total Queries</span>
                    <span class="metric-value">${analytics.totalQueries || 0}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Avg Documents per Query</span>
                    <span class="metric-value">${(usage.avgDocumentsPerQuery || 0).toFixed(1)}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Avg Context Length</span>
                    <span class="metric-value">${Math.round(usage.avgContextLength || 0)} chars</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Avg Response Length</span>
                    <span class="metric-value">${Math.round(usage.avgResponseLength || 0)} chars</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Avg Query Length</span>
                    <span class="metric-value">${Math.round(usage.avgQueryLength || 0)} chars</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Cache Hit Rate</span>
                    <span class="metric-value">${(usage.cacheHitRate || 0).toFixed(1)}%</span>
                </div>
            `;
        }

        function updateAIModelUsage(analytics) {
            const container = document.getElementById('aiModelUsage');
            const ai = analytics.ai || {};

            let modelUsageHtml = '';
            if (ai.modelsUsed && Object.keys(ai.modelsUsed).length > 0) {
                Object.entries(ai.modelsUsed).forEach(([model, count]) => {
                    modelUsageHtml += `
                        <div class="metric">
                            <span class="metric-label">${model}</span>
                            <span class="metric-value">${count}</span>
                        </div>
                    `;
                });
            } else {
                modelUsageHtml = '<div class="loading">No model usage data available</div>';
            }

            container.innerHTML = `
                <div class="metric">
                    <span class="metric-label">Avg Tokens Used</span>
                    <span class="metric-value">${Math.round(ai.avgTokensUsed || 0)}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Streaming Usage</span>
                    <span class="metric-value">${(ai.streamingRate || 0).toFixed(1)}%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Total Tokens</span>
                    <span class="metric-value">${(ai.totalTokensUsed || 0).toLocaleString()}</span>
                </div>
                <h4 style="margin: 20px 0 10px 0; color: #333;">Model Breakdown:</h4>
                ${modelUsageHtml}
            `;
        }

        function updateErrorTracking(analytics) {
            const container = document.getElementById('errorTracking');
            const errors = analytics.errors || {};

            container.innerHTML = `
                <div class="metric">
                    <span class="metric-label">Total Errors</span>
                    <span class="metric-value">${errors.totalErrors || 0}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Error Rate</span>
                    <span class="metric-value">${(errors.errorRate || 0).toFixed(2)}%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Total Warnings</span>
                    <span class="metric-value">${errors.totalWarnings || 0}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Warning Rate</span>
                    <span class="metric-value">${(errors.warningRate || 0).toFixed(2)}%</span>
                </div>
            `;
        }

        function updateBusinessMetrics(analytics) {
            const container = document.getElementById('businessMetrics');
            const business = analytics.business || {};

            let subscriptionHtml = '';
            if (business.subscriptionBreakdown && Object.keys(business.subscriptionBreakdown).length > 0) {
                Object.entries(business.subscriptionBreakdown).forEach(([type, count]) => {
                    subscriptionHtml += `
                        <div class="metric">
                            <span class="metric-label">${type.charAt(0).toUpperCase() + type.slice(1)}</span>
                            <span class="metric-value">${count}</span>
                        </div>
                    `;
                });
            }

            container.innerHTML = `
                <div class="metric">
                    <span class="metric-label">Total Credits Used</span>
                    <span class="metric-value">${(business.totalCreditsUsed || 0).toLocaleString()}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Avg Credits per Query</span>
                    <span class="metric-value">${(business.avgCreditsPerQuery || 0).toFixed(2)}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Premium Usage</span>
                    <span class="metric-value">${(business.premiumRate || 0).toFixed(1)}%</span>
                </div>
                <h4 style="margin: 20px 0 10px 0; color: #333;">Subscription Breakdown:</h4>
                ${subscriptionHtml || '<div class="loading">No subscription data available</div>'}
            `;
        }

        function updateCharts(analytics) {
            // This would be implemented with real-time chart data
            // For now, we'll show placeholder charts
            const perfCtx = document.getElementById('performanceChart').getContext('2d');
            const usageCtx = document.getElementById('usageChart').getContext('2d');

            if (performanceChart) performanceChart.destroy();
            if (usageChart) usageChart.destroy();

            // Performance chart
            performanceChart = new Chart(perfCtx, {
                type: 'line',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                    datasets: [{
                        label: 'Response Time (ms)',
                        data: [120, 150, 180, 200, 160, 140],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Usage chart
            usageChart = new Chart(usageCtx, {
                type: 'bar',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                    datasets: [{
                        label: 'Queries',
                        data: [45, 32, 67, 89, 76, 54],
                        backgroundColor: 'rgba(102, 126, 234, 0.8)',
                        borderColor: '#667eea',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        async function exportData() {
            if (!apiKey) {
                showMessage('❌ Not authenticated. Please login first.', 'error');
                return;
            }

            const timeRange = document.getElementById('timeRange').value;
            const startDate = new Date();
            const endDate = new Date();

            // Calculate date range based on timeRange
            switch (timeRange) {
                case '1h':
                    startDate.setHours(startDate.getHours() - 1);
                    break;
                case '6h':
                    startDate.setHours(startDate.getHours() - 6);
                    break;
                case '24h':
                    startDate.setDate(startDate.getDate() - 1);
                    break;
                case '7d':
                    startDate.setDate(startDate.getDate() - 7);
                    break;
                case '30d':
                    startDate.setDate(startDate.getDate() - 30);
                    break;
            }

            try {
                const response = await fetch(`${API_BASE}/api/v1/analytics/export?startDate=${startDate.toISOString().split('T')[0]}&endDate=${endDate.toISOString().split('T')[0]}&format=csv&apiKey=${apiKey}`);

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `chatai-analytics-${startDate.toISOString().split('T')[0]}-to-${endDate.toISOString().split('T')[0]}.csv`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    showMessage('✅ Data exported successfully!', 'success');
                } else {
                    throw new Error('Export failed');
                }
            } catch (error) {
                showMessage('❌ Failed to export data', 'error');
            }
        }

        function showMessage(message, type) {
            const messagesContainer = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            messageDiv.textContent = message;

            messagesContainer.innerHTML = '';
            messagesContainer.appendChild(messageDiv);

            if (type !== 'error') {
                setTimeout(() => {
                    messageDiv.remove();
                }, 5000);
            }
        }

        function formatUptime(seconds) {
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);

            if (days > 0) {
                return `${days}d ${hours}h ${minutes}m`;
            } else if (hours > 0) {
                return `${hours}h ${minutes}m`;
            } else {
                return `${minutes}m`;
            }
        }
    </script>
</body>

</html>