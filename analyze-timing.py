#!/usr/bin/env python3

import requests
import time
import json
from datetime import datetime
import re

def test_api_timing():
    """Test API timing with detailed analysis of session vs first content delay"""
    
    url = "https://chatai.abstraxn.com/api/v1/chat/"
    params = {
        "apikey": "5gWtRAYp7PXnW6rXOVaxXCLPcJ8Pad1k",
        "query": "explain policies",
        "stream": "true"
    }
    
    print("🔍 Detailed Timing Analysis")
    print("=" * 50)
    
    for test_num in range(1, 4):
        print(f"\n🧪 Test {test_num}:")
        print("-" * 20)
        
        # Record overall start time
        overall_start = time.time()
        
        try:
            # Make streaming request
            response = requests.get(url, params=params, stream=True, timeout=30)
            
            session_time = None
            first_content_time = None
            content_count = 0
            
            # Process streaming response
            for line in response.iter_lines(decode_unicode=True):
                current_time = time.time()
                
                if line.startswith('data: '):
                    try:
                        data = json.loads(line[6:])  # Remove 'data: ' prefix
                        
                        if data.get('type') == 'session':
                            session_time = current_time
                            session_timestamp = data.get('timestamp', '')
                            print(f"📅 Session initialized: {session_timestamp}")
                            print(f"⏱️  Time to session: {session_time - overall_start:.3f}s")
                        
                        elif data.get('type') == 'content':
                            if first_content_time is None:
                                first_content_time = current_time
                                content = data.get('content', '')
                                print(f"🎯 First content received: '{content}'")
                                
                                if session_time:
                                    delay = first_content_time - session_time
                                    print(f"⚡ Session → First Content delay: {delay:.3f}s")
                                    
                                    # This is the key metric we're trying to optimize
                                    if delay < 5.0:
                                        print(f"✅ GOOD: Delay under 5 seconds!")
                                    elif delay < 7.0:
                                        print(f"⚠️  IMPROVED: Delay reduced but still room for improvement")
                                    else:
                                        print(f"❌ SLOW: Delay still over 7 seconds")
                            
                            content_count += 1
                            if content_count >= 5:  # Stop after first few content chunks
                                break
                        
                        elif data.get('type') == 'done':
                            total_time = current_time - overall_start
                            print(f"🏁 Total response time: {total_time:.3f}s")
                            break
                            
                    except json.JSONDecodeError:
                        continue
            
            # Summary for this test
            if session_time and first_content_time:
                total_delay = first_content_time - session_time
                print(f"📊 Summary: {total_delay:.3f}s delay between session and first content")
            else:
                print("❌ Could not measure timing properly")
                
        except Exception as e:
            print(f"❌ Error in test {test_num}: {e}")
        
        if test_num < 3:
            print("\n⏳ Waiting 2 seconds before next test...")
            time.sleep(2)
    
    print("\n" + "=" * 50)
    print("🎯 OPTIMIZATION TARGET:")
    print("   - Before: ~7 seconds between session and first content")
    print("   - Target: ~3-4 seconds between session and first content")
    print("   - Current: Check the delays measured above")
    print("\n💡 Parallel processing should reduce this delay by:")
    print("   1. Running API validation + semantic setup in parallel")
    print("   2. Preparing vector search while doing semantic refinement")
    print("   3. Pre-warming AI model connections")

if __name__ == "__main__":
    test_api_timing()
