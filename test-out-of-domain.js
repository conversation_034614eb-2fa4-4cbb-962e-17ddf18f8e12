/**
 * Test script for enhanced out-of-domain detection
 * Tests various strategies for handling out-of-domain queries
 */

const semanticValidationService = require('./src/services/semanticValidationService');
const outOfDomainResponseService = require('./src/services/outOfDomainResponseService');

// Test queries - mix of in-domain, out-of-domain, and edge cases
const testQueries = [
    // Clear out-of-domain queries
    {
        query: "Who is the PM of India?",
        category: "politics",
        expectedOutOfDomain: true
    },
    {
        query: "What's the weather like today?",
        category: "weather", 
        expectedOutOfDomain: true
    },
    {
        query: "Tell me about the latest movie releases",
        category: "entertainment",
        expectedOutOfDomain: true
    },
    {
        query: "How do I code in JavaScript?",
        category: "technology",
        expectedOutOfDomain: true
    },
    {
        query: "What is the capital of France?",
        category: "general_knowledge",
        expectedOutOfDomain: true
    },
    
    // Clear in-domain queries
    {
        query: "What is the travel policy?",
        category: "hr_policy",
        expectedOutOfDomain: false
    },
    {
        query: "How do I claim travel expenses?",
        category: "hr_policy",
        expectedOutOfDomain: false
    },
    {
        query: "What are the hotel booking guidelines?",
        category: "hr_policy",
        expectedOutOfDomain: false
    },
    
    // Edge cases - potentially ambiguous
    {
        query: "Can I travel to Europe for work?",
        category: "edge_case",
        expectedOutOfDomain: false // Should be in-domain due to work context
    },
    {
        query: "What's the company phone policy?",
        category: "edge_case", 
        expectedOutOfDomain: false // Should be in-domain
    },
    {
        query: "I need help with my personal taxes",
        category: "edge_case",
        expectedOutOfDomain: true // Personal, not work-related
    }
];

async function testOutOfDomainDetection() {
    console.log('🧪 TESTING ENHANCED OUT-OF-DOMAIN DETECTION\n');
    console.log('=' .repeat(80));
    
    const results = [];
    
    for (const testCase of testQueries) {
        console.log(`\n🔍 Testing: "${testCase.query}"`);
        console.log(`   Expected: ${testCase.expectedOutOfDomain ? 'OUT-OF-DOMAIN' : 'IN-DOMAIN'}`);
        console.log(`   Category: ${testCase.category}`);
        
        try {
            // Mock semantic result for testing (since we don't have real vector search)
            const mockSimilarityResult = {
                maxScore: testCase.expectedOutOfDomain ? 0.2 : 0.8,
                relevantChunks: testCase.expectedOutOfDomain ? [] : [
                    { text: "Sample policy text", score: 0.8, filename: "travel_policy.pdf" }
                ],
                totalChunks: testCase.expectedOutOfDomain ? 0 : 1
            };
            
            // Test the out-of-domain detection directly
            const outOfDomainResult = semanticValidationService.detectOutOfDomain(
                testCase.query, 
                mockSimilarityResult, 
                []
            );
            
            // Test response generation
            const response = outOfDomainResponseService.generateResponse(
                testCase.query,
                outOfDomainResult,
                { semanticScore: mockSimilarityResult.maxScore }
            );
            
            // Evaluate results
            const isCorrect = outOfDomainResult.isOutOfDomain === testCase.expectedOutOfDomain;
            const status = isCorrect ? '✅ CORRECT' : '❌ INCORRECT';
            
            console.log(`   Result: ${outOfDomainResult.isOutOfDomain ? 'OUT-OF-DOMAIN' : 'IN-DOMAIN'} ${status}`);
            console.log(`   Confidence: ${outOfDomainResult.confidence.toFixed(3)}`);
            console.log(`   Reasoning: ${outOfDomainResult.reasoning}`);
            
            if (outOfDomainResult.isOutOfDomain) {
                console.log(`   Response: ${response.message}`);
                console.log(`   Suggestions: ${response.suggestions.slice(0, 2).join('; ')}`);
            }
            
            results.push({
                query: testCase.query,
                expected: testCase.expectedOutOfDomain,
                actual: outOfDomainResult.isOutOfDomain,
                correct: isCorrect,
                confidence: outOfDomainResult.confidence,
                category: testCase.category
            });
            
        } catch (error) {
            console.log(`   ❌ ERROR: ${error.message}`);
            results.push({
                query: testCase.query,
                expected: testCase.expectedOutOfDomain,
                actual: null,
                correct: false,
                error: error.message,
                category: testCase.category
            });
        }
    }
    
    // Summary
    console.log('\n' + '=' .repeat(80));
    console.log('📊 TEST SUMMARY');
    console.log('=' .repeat(80));
    
    const correct = results.filter(r => r.correct).length;
    const total = results.length;
    const accuracy = (correct / total * 100).toFixed(1);
    
    console.log(`Overall Accuracy: ${correct}/${total} (${accuracy}%)`);
    
    // Category breakdown
    const categories = [...new Set(results.map(r => r.category))];
    categories.forEach(category => {
        const categoryResults = results.filter(r => r.category === category);
        const categoryCorrect = categoryResults.filter(r => r.correct).length;
        const categoryAccuracy = (categoryCorrect / categoryResults.length * 100).toFixed(1);
        console.log(`${category}: ${categoryCorrect}/${categoryResults.length} (${categoryAccuracy}%)`);
    });
    
    // Failed cases
    const failed = results.filter(r => !r.correct);
    if (failed.length > 0) {
        console.log('\n❌ FAILED CASES:');
        failed.forEach(f => {
            console.log(`   "${f.query}" - Expected: ${f.expected}, Got: ${f.actual}`);
        });
    }
    
    console.log('\n✅ Testing complete!');
}

// Test specific strategies
async function testStrategies() {
    console.log('\n🔬 TESTING INDIVIDUAL STRATEGIES\n');
    
    const testQuery = "Who is the PM of India?";
    console.log(`Testing query: "${testQuery}"`);
    
    // Test domain keyword analysis
    const domainAnalysis = semanticValidationService.analyzeDomainKeywords(testQuery.toLowerCase());
    console.log('\n📝 Domain Keywords Analysis:');
    console.log(`   Has domain keywords: ${domainAnalysis.hasDomainKeywords}`);
    console.log(`   Found keywords: ${domainAnalysis.foundKeywords.join(', ') || 'none'}`);
    console.log(`   Confidence: ${domainAnalysis.confidence}`);
    
    // Test pattern analysis
    const patternAnalysis = semanticValidationService.analyzeOutOfDomainPatterns(testQuery);
    console.log('\n🎯 Pattern Analysis:');
    console.log(`   Is out-of-domain: ${patternAnalysis.isOutOfDomain}`);
    console.log(`   Matched patterns: ${patternAnalysis.matchedPatterns.join(', ') || 'none'}`);
    console.log(`   Confidence: ${patternAnalysis.confidence}`);
    
    // Test semantic analysis
    const semanticAnalysis = semanticValidationService.analyzeSemanticScore(0.15);
    console.log('\n📊 Semantic Analysis (score: 0.15):');
    console.log(`   Is low similarity: ${semanticAnalysis.isLowSimilarity}`);
    console.log(`   Confidence: ${semanticAnalysis.confidence}`);
    console.log(`   Reasoning: ${semanticAnalysis.reasoning}`);
    
    // Test intent classification
    const intentAnalysis = semanticValidationService.classifyQueryIntent(testQuery.toLowerCase());
    console.log('\n🎭 Intent Classification:');
    console.log(`   Intent: ${intentAnalysis.intent}`);
    console.log(`   Is out-of-scope: ${intentAnalysis.isOutOfScope}`);
    console.log(`   Confidence: ${intentAnalysis.confidence}`);
}

// Run tests
async function runTests() {
    try {
        await testOutOfDomainDetection();
        await testStrategies();
    } catch (error) {
        console.error('Test execution failed:', error);
    }
}

// Export for use in other tests
module.exports = {
    testQueries,
    testOutOfDomainDetection,
    testStrategies,
    runTests
};

// Run if called directly
if (require.main === module) {
    runTests();
}
