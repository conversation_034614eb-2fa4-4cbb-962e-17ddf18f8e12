/**
 * Analytics Authentication Middleware
 * Protects analytics endpoints with API key authentication
 */

const ANALYTICS_API_KEY = 'pass@123';

function analyticsAuthMiddleware(req, res, next) {
    // Get API key from query parameter or Authorization header
    const apiKey = req.query.apiKey ||
        req.headers.authorization?.replace('Bearer ', '') ||
        req.headers['x-api-key'];

    // Check if API key is provided and valid
    if (!apiKey) {
        return res.status(401).json({
            error: true,
            message: 'API key is required for analytics access',
            hint: 'Provide API key via ?apiKey=your-key or Authorization: Bearer your-key header'
        });
    }

    if (apiKey !== ANALYTICS_API_KEY) {
        return res.status(403).json({
            error: true,
            message: 'Invalid API key for analytics access'
        });
    }

    // API key is valid, proceed to next middleware/route
    next();
}

module.exports = analyticsAuthMiddleware; 