const rateLimit = require('express-rate-limit');

// Factory function to create rate limiters with async config
function createRateLimitMiddleware(config) {
  // Create general rate limiter
  const generalRateLimit = rateLimit({
    windowMs: config.rateLimit.windowMinutes * 60 * 1000, // Convert to milliseconds
    max: config.rateLimit.maxRequests, // Limit each IP to max requests per windowMs
    message: {
      error: true,
      message: `Too many requests from this IP, please try again after ${config.rateLimit.windowMinutes} minutes.`,
      retryAfter: config.rateLimit.windowMinutes * 60 // seconds
    },
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers

    // Custom key generator (can be enhanced to use user ID instead of IP)
    keyGenerator: (req) => {
      // Try to use user ID from auth token if available
      const authToken = req.headers.authorization;
      if (authToken) {
        try {
          // In a real implementation, you'd decode the JWT to get user ID
          // For now, we'll use IP + a hash of the token
          const tokenHash = require('crypto')
            .createHash('md5')
            .update(authToken)
            .digest('hex')
            .substring(0, 8);
          return `${req.ip}_${tokenHash}`;
        } catch (error) {
          console.warn('Failed to process auth token for rate limiting:', error.message);
          return req.ip;
        }
      }
      return req.ip;
    },

    // Custom handler for rate limit exceeded
    handler: (req, res) => {
      // console.warn(`Rate limit exceeded for IP: ${req.ip}`);
      res.status(429).json({
        error: true,
        message: `Too many requests from this IP, please try again after ${config.rateLimit.windowMinutes} minutes.`,
        retryAfter: config.rateLimit.windowMinutes * 60,
        timestamp: new Date().toISOString()
      });
    },

    // Skip rate limiting for health checks
    skip: (req) => {
      return req.path === '/health' || req.path === '/health/aws';
    }
  });

  // Create stricter chat rate limiter for API endpoints
  const chatRateLimit = rateLimit({
    windowMs: config.rateLimit.windowMinutes * 60 * 1000,
    max: Math.floor(config.rateLimit.maxRequests * 0.5), // 50% of general limit for chat
    message: {
      error: true,
      message: `Too many chat requests from this IP, please try again after ${config.rateLimit.windowMinutes} minutes.`,
      retryAfter: config.rateLimit.windowMinutes * 60
    },
    standardHeaders: true,
    legacyHeaders: false,

    keyGenerator: (req) => {
      const authToken = req.headers.authorization;
      if (authToken) {
        try {
          const tokenHash = require('crypto')
            .createHash('md5')
            .update(authToken)
            .digest('hex')
            .substring(0, 8);
          return `chat_${req.ip}_${tokenHash}`;
        } catch (error) {
          return `chat_${req.ip}`;
        }
      }
      return `chat_${req.ip}`;
    },

    handler: (req, res) => {
      console.warn(`Chat rate limit exceeded for IP: ${req.ip}`);
      res.status(429).json({
        error: true,
        message: `Too many chat requests from this IP, please try again after ${config.rateLimit.windowMinutes} minutes.`,
        retryAfter: config.rateLimit.windowMinutes * 60,
        timestamp: new Date().toISOString()
      });
    },

    skip: (req) => {
      return req.path === '/health' || req.path === '/health/aws';
    }
  });

  return {
    generalRateLimit,
    chatRateLimit
  };
}

// Export factory function and default instances for backwards compatibility
module.exports = {
  createRateLimitMiddleware,
  // For backwards compatibility, export default rate limiters with fallback values
  rateLimit: rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Default values
    message: {
      error: true,
      message: 'Too many requests from this IP, please try again after 15 minutes.',
      retryAfter: 15 * 60
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => req.path === '/health' || req.path === '/health/aws'
  }),
  chatRateLimit: rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 50,
    message: {
      error: true,
      message: 'Too many chat requests from this IP, please try again after 15 minutes.',
      retryAfter: 15 * 60
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => req.path === '/health' || req.path === '/health/aws'
  })
};
