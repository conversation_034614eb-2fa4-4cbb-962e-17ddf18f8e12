/**
 * Prompt Preprocessor Service
 * Provides semantic refinement for user queries through context-aware enhancement,
 * query expansion, and hybrid rule-based + AI-powered processing
 */

const QueryAnalyzer = require('./preprocessing/queryAnalyzer');
const ContextEnhancer = require('./preprocessing/contextEnhancer');
const QueryExpander = require('./preprocessing/queryExpander');
const config = require('../config/semanticRefinement');

class PromptPreprocessorService {
  constructor() {
    this.queryAnalyzer = new QueryAnalyzer(config);
    this.contextEnhancer = new ContextEnhancer(config);
    this.queryExpander = new QueryExpander(config);

    // Use configuration from config file
    this.config = config;

    // Cache for refined queries
    this.refinementCache = new Map();
    this.cacheTimeout = config.cache.ttl;
    this.maxCacheSize = config.cache.maxSize;
  }

  /**
   * Main method to refine user queries
   * @param {string} originalQuery - The original user query
   * @param {Array} conversationHistory - Previous conversation messages
   * @param {Object} contextMetadata - Additional context information
   * @param {string} sessionId - Session identifier
   * @returns {Object} Refinement result with original and refined query
   */
  async refineQuery(originalQuery, conversationHistory = [], contextMetadata = {}, sessionId = null) {
    if (!this.config.enabled) {
      if (this.config.logging.enabled) {
        console.log(`🔇 Semantic refinement disabled - returning original query`);
      }
      return {
        original: originalQuery,
        refined: originalQuery,
        refinements: [],
        cached: false,
        processingTime: 0
      };
    }

    const startTime = Date.now();

    try {
      console.log(`🔍 Starting semantic refinement for query: "${originalQuery.substring(0, 50)}..."`);

      // Check cache first
      const cacheKey = this.generateCacheKey(originalQuery, sessionId);
      if (this.config.cacheRefinements && this.refinementCache.has(cacheKey)) {
        const cached = this.refinementCache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          console.log(`⚡ Using cached refinement for query`);
          return {
            ...cached.result,
            cached: true,
            processingTime: Date.now() - startTime
          };
        } else {
          this.refinementCache.delete(cacheKey);
        }
      }

      // Step 1: Analyze the query
      const analysis = await this.queryAnalyzer.analyzeQuery(originalQuery, conversationHistory);
      console.log(`📊 Query analysis: ${JSON.stringify(analysis, null, 2)}`);

      // Step 2: Enhance with conversation context
      const contextEnhanced = await this.contextEnhancer.enhanceWithContext(
        originalQuery,
        conversationHistory.slice(-this.config.contextWindow),
        analysis
      );

      // Step 3: Expand the query if needed
      const expanded = await this.queryExpander.expandQuery(
        contextEnhanced.enhanced || originalQuery,
        analysis,
        contextMetadata,
        this.config.useAI
      );

      // Step 4: Compile final refined query
      const refinedQuery = this.compileRefinedQuery(originalQuery, contextEnhanced, expanded, analysis);

      const result = {
        original: originalQuery,
        refined: refinedQuery,
        refinements: [
          ...contextEnhanced.refinements || [],
          ...expanded.refinements || []
        ],
        analysis,
        cached: false,
        processingTime: Date.now() - startTime
      };

      // Cache the result
      if (this.config.cacheRefinements) {
        this.refinementCache.set(cacheKey, {
          result,
          timestamp: Date.now()
        });
      }

      console.log(`✨ Query refinement completed in ${result.processingTime}ms`);
      console.log(`📝 Original: "${originalQuery}"`);
      console.log(`🎯 Refined: "${refinedQuery}"`);

      return result;

    } catch (error) {
      console.error(`❌ Error in query refinement:`, error);
      return {
        original: originalQuery,
        refined: originalQuery,
        refinements: [],
        error: error.message,
        cached: false,
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * Compile the final refined query from all enhancement steps
   */
  compileRefinedQuery(original, contextEnhanced, expanded, analysis) {
    let refined = contextEnhanced.enhanced || original;

    // Add expansions if they improve the query
    if (expanded.expansions && expanded.expansions.length > 0) {
      const relevantExpansions = expanded.expansions.slice(0, this.config.expansionLimit);

      // For ambiguous queries, add clarifying terms
      if (analysis.ambiguity > 0.6) {
        refined += ` (related to: ${relevantExpansions.join(', ')})`;
      }
      // For incomplete queries, integrate expansions naturally
      else if (analysis.completeness < 0.7) {
        refined = `${refined} ${relevantExpansions.join(' ')}`;
      }
    }

    // Ensure we don't exceed max length
    if (refined.length > this.config.maxQueryLength) {
      refined = refined.substring(0, this.config.maxQueryLength - 3) + '...';
    }

    return refined.trim();
  }

  /**
   * Generate cache key for query refinement
   */
  generateCacheKey(query, sessionId) {
    const queryHash = require('crypto').createHash('md5').update(query.toLowerCase()).digest('hex');
    return `refinement:${sessionId || 'global'}:${queryHash}`;
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log(`⚙️ Updated preprocessor config:`, this.config);
  }

  /**
   * Clear refinement cache
   */
  clearCache() {
    this.refinementCache.clear();
    console.log(`🧹 Cleared refinement cache`);
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: this.refinementCache.size,
      timeout: this.cacheTimeout,
      enabled: this.config.cache.enabled
    };
  }
}

module.exports = PromptPreprocessorService;
