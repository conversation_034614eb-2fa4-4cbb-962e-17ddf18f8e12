/**
 * Query Analyzer
 * Analyzes user queries for intent, ambiguity, completeness, and other semantic properties
 */

class QueryAnalyzer {
  constructor() {
    // Common patterns for analysis
    this.patterns = {
      questions: /\b(what|how|when|where|why|who|which|can|could|would|should|is|are|do|does|did)\b/i,
      incomplete: /\b(this|that|it|they|them|here|there)\b/i,
      vague: /\b(something|anything|stuff|things|some|any)\b/i,
      commands: /\b(show|find|get|give|tell|explain|describe|list|search)\b/i,
      temporal: /\b(today|yesterday|tomorrow|now|recently|latest|current|last|next|previous)\b/i,
      comparative: /\b(better|worse|best|worst|more|less|compare|versus|vs|than)\b/i
    };

    // Domain-specific indicators (can be customized based on your document corpus)
    this.domainIndicators = {
      technical: /\b(api|database|server|code|function|method|class|variable|error|bug|debug)\b/i,
      business: /\b(revenue|profit|sales|customer|client|market|strategy|budget|cost)\b/i,
      process: /\b(workflow|procedure|step|process|method|approach|way|how to)\b/i,
      data: /\b(report|analytics|metrics|data|statistics|numbers|chart|graph)\b/i
    };
  }

  /**
   * Analyze a query for various semantic properties
   * @param {string} query - The user query to analyze
   * @param {Array} conversationHistory - Previous conversation context
   * @returns {Object} Analysis results
   */
  async analyzeQuery(query, conversationHistory = []) {
    const analysis = {
      intent: this.classifyIntent(query),
      ambiguity: this.calculateAmbiguity(query, conversationHistory),
      completeness: this.calculateCompleteness(query, conversationHistory),
      domain: this.identifyDomain(query),
      complexity: this.calculateComplexity(query),
      temporal: this.hasTemporalReference(query),
      needsContext: this.needsConversationContext(query, conversationHistory),
      queryType: this.classifyQueryType(query),
      confidence: 0
    };

    // Calculate overall confidence score
    analysis.confidence = this.calculateConfidence(analysis, query);

    return analysis;
  }

  /**
   * Classify the intent of the query
   */
  classifyIntent(query) {
    const intents = [];
    
    if (this.patterns.questions.test(query)) {
      intents.push('question');
    }
    if (this.patterns.commands.test(query)) {
      intents.push('command');
    }
    if (this.patterns.comparative.test(query)) {
      intents.push('comparison');
    }
    
    // Default to information seeking if no specific intent found
    return intents.length > 0 ? intents : ['information_seeking'];
  }

  /**
   * Calculate ambiguity score (0-1, higher = more ambiguous)
   */
  calculateAmbiguity(query, conversationHistory) {
    let ambiguityScore = 0;
    
    // Check for vague terms
    if (this.patterns.vague.test(query)) {
      ambiguityScore += 0.3;
    }
    
    // Check for pronouns without clear antecedents
    if (this.patterns.incomplete.test(query)) {
      // If there's recent conversation history, pronouns are less ambiguous
      if (conversationHistory.length === 0) {
        ambiguityScore += 0.4;
      } else {
        ambiguityScore += 0.2;
      }
    }
    
    // Very short queries are often ambiguous
    if (query.length < 10) {
      ambiguityScore += 0.3;
    }
    
    // Single word queries are highly ambiguous
    if (query.trim().split(/\s+/).length === 1) {
      ambiguityScore += 0.4;
    }
    
    return Math.min(ambiguityScore, 1.0);
  }

  /**
   * Calculate completeness score (0-1, higher = more complete)
   */
  calculateCompleteness(query, conversationHistory) {
    let completenessScore = 0.5; // Start with neutral
    
    // Longer queries tend to be more complete
    const wordCount = query.trim().split(/\s+/).length;
    if (wordCount >= 5) {
      completenessScore += 0.3;
    } else if (wordCount >= 3) {
      completenessScore += 0.1;
    } else {
      completenessScore -= 0.2;
    }
    
    // Questions with specific question words are more complete
    if (this.patterns.questions.test(query) && wordCount >= 3) {
      completenessScore += 0.2;
    }
    
    // Commands with objects are more complete
    if (this.patterns.commands.test(query) && wordCount >= 2) {
      completenessScore += 0.2;
    }
    
    // Queries with context references but no history are incomplete
    if (this.patterns.incomplete.test(query) && conversationHistory.length === 0) {
      completenessScore -= 0.3;
    }
    
    return Math.max(0, Math.min(completenessScore, 1.0));
  }

  /**
   * Identify the domain/topic of the query
   */
  identifyDomain(query) {
    const domains = [];
    
    for (const [domain, pattern] of Object.entries(this.domainIndicators)) {
      if (pattern.test(query)) {
        domains.push(domain);
      }
    }
    
    return domains.length > 0 ? domains : ['general'];
  }

  /**
   * Calculate query complexity (0-1, higher = more complex)
   */
  calculateComplexity(query) {
    let complexity = 0;
    
    const wordCount = query.trim().split(/\s+/).length;
    const sentenceCount = query.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
    
    // Word count contribution
    if (wordCount > 15) complexity += 0.4;
    else if (wordCount > 8) complexity += 0.2;
    
    // Multiple sentences indicate complexity
    if (sentenceCount > 1) complexity += 0.3;
    
    // Multiple intents indicate complexity
    const intents = this.classifyIntent(query);
    if (intents.length > 1) complexity += 0.2;
    
    // Comparative queries are complex
    if (this.patterns.comparative.test(query)) complexity += 0.2;
    
    return Math.min(complexity, 1.0);
  }

  /**
   * Check if query has temporal references
   */
  hasTemporalReference(query) {
    return this.patterns.temporal.test(query);
  }

  /**
   * Determine if query needs conversation context
   */
  needsConversationContext(query, conversationHistory) {
    // Queries with pronouns or references need context
    if (this.patterns.incomplete.test(query)) {
      return true;
    }
    
    // Very short queries often need context
    if (query.trim().split(/\s+/).length <= 2) {
      return true;
    }
    
    // Follow-up indicators
    const followUpPatterns = /\b(also|too|additionally|furthermore|moreover|and|plus)\b/i;
    if (followUpPatterns.test(query)) {
      return true;
    }
    
    return false;
  }

  /**
   * Classify the type of query
   */
  classifyQueryType(query) {
    if (this.patterns.questions.test(query)) {
      return 'question';
    }
    if (this.patterns.commands.test(query)) {
      return 'command';
    }
    if (this.patterns.comparative.test(query)) {
      return 'comparison';
    }
    
    // Check for specific patterns
    if (/\b(define|definition|meaning|what is)\b/i.test(query)) {
      return 'definition';
    }
    if (/\b(example|sample|instance)\b/i.test(query)) {
      return 'example_request';
    }
    if (/\b(help|assist|support)\b/i.test(query)) {
      return 'help_request';
    }
    
    return 'general';
  }

  /**
   * Calculate overall confidence in the analysis
   */
  calculateConfidence(analysis, query) {
    let confidence = 0.5; // Start with neutral confidence
    
    // Higher confidence for longer, more specific queries
    const wordCount = query.trim().split(/\s+/).length;
    if (wordCount >= 5) confidence += 0.2;
    if (wordCount >= 10) confidence += 0.1;
    
    // Lower confidence for highly ambiguous queries
    if (analysis.ambiguity > 0.7) confidence -= 0.3;
    
    // Higher confidence for complete queries
    if (analysis.completeness > 0.7) confidence += 0.2;
    
    // Domain-specific queries have higher confidence
    if (analysis.domain.length > 0 && !analysis.domain.includes('general')) {
      confidence += 0.1;
    }
    
    return Math.max(0.1, Math.min(confidence, 1.0));
  }
}

module.exports = QueryAnalyzer;
