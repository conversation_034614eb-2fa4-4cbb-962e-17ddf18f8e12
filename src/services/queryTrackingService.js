const { DataSource } = require('typeorm');
const { databaseConfigManager } = require('../config/database');

// Import entities
const ChatAiMessage = require('../entities/ChatAiMessage');
const ChatAiDocument = require('../entities/ChatAiDocument');
const ChatAi = require('../entities/ChatAi');

class QueryTrackingService {
    constructor() {
        this.dataSource = null;
        this.isConnected = false;
        this.trackingEnabled = process.env.ENABLE_QUERY_TRACKING !== 'false'; // Enabled by default
    }

    /**
     * Initialize database connection for tracking
     */
    async initialize() {
        if (!this.trackingEnabled) {
            console.log('📊 Query tracking disabled via environment variable');
            return;
        }

        try {
            console.log('📊 Initializing query tracking service...');

            const databaseConfig = await databaseConfigManager.initialize();

            this.dataSource = new DataSource({
                ...databaseConfig,
                entities: [<PERSON><PERSON><PERSON><PERSON>Message, ChatAiDocument, ChatAi],
            });

            await this.dataSource.initialize();
            this.isConnected = true;

            console.log('✅ Query tracking service initialized successfully');

        } catch (error) {
            console.error('❌ Query tracking service initialization failed:', error.message);
            this.trackingEnabled = false;
        }
    }

    /**
     * Track a complete query-response interaction
     */
    async trackQueryInteraction(trackingData) {
        if (!this.trackingEnabled || !this.isConnected) {
            return { success: false, reason: 'tracking_disabled' };
        }

        try {
            const {
                // Core data
                chatAiId,
                userId,
                sessionId,
                query,
                response,

                // Performance metrics
                timing,
                performance,

                // System state
                documentsUsed,
                contextLength,
                cacheHits,

                // AI model info
                modelUsed,
                tokensUsed,

                // Error tracking
                errors,
                warnings,

                // User behavior
                userAgent,
                ipAddress,
                origin,

                // Business metrics
                creditsDeducted,
                subscriptionStatus,

                // Additional metadata
                metadata = {}
            } = trackingData;

            // Create comprehensive tracking record
            const trackingRecord = {
                // Core message data
                question: query,
                response: response,
                chatAiId: chatAiId,
                userId: userId,
                sessionId: sessionId,
                timestamp: new Date(),

                // Performance tracking
                performanceMetrics: {
                    totalDuration: timing?.total || 0,
                    apiValidationTime: timing?.apiValidation || 0,
                    creditDeductionTime: timing?.creditDeduction || 0,
                    semanticRefinementTime: timing?.semanticRefinement || 0,
                    vectorSearchTime: timing?.vectorSearch || 0,
                    openRouterTime: timing?.openRouter || 0,
                    firstChunkTime: timing?.firstChunk || 0,
                    cacheRetrievalTime: timing?.cacheRetrieval || 0,
                },

                // System performance
                systemMetrics: {
                    documentsUsed: documentsUsed || 0,
                    contextLength: contextLength || 0,
                    responseLength: response?.length || 0,
                    queryLength: query?.length || 0,
                    cacheHits: cacheHits || {},
                    memoryUsage: process.memoryUsage(),
                    cpuUsage: process.cpuUsage(),
                },

                // AI model metrics
                aiMetrics: {
                    modelUsed: modelUsed || 'unknown',
                    tokensUsed: tokensUsed || 0,
                    temperature: metadata.temperature || 0.7,
                    maxTokens: metadata.maxTokens || 1000,
                    streaming: metadata.streaming || false,
                },

                // Error tracking
                errorTracking: {
                    hasErrors: !!(errors && errors.length > 0),
                    errors: errors || [],
                    warnings: warnings || [],
                    errorCount: errors?.length || 0,
                    warningCount: warnings?.length || 0,
                },

                // User behavior
                userBehavior: {
                    userAgent: userAgent || 'unknown',
                    ipAddress: ipAddress || 'unknown',
                    origin: origin || 'unknown',
                    isTestMode: metadata.testMode || false,
                    requestMethod: metadata.requestMethod || 'POST',
                },

                // Business metrics
                businessMetrics: {
                    creditsDeducted: creditsDeducted || 0,
                    subscriptionStatus: subscriptionStatus || 'unknown',
                    isPremium: subscriptionStatus === 'pro' || subscriptionStatus === 'enterprise',
                    queryType: metadata.queryType || 'chat_query',
                },

                // Additional metadata
                metadata: {
                    ...metadata,
                    trackingVersion: '1.0',
                    serviceVersion: process.env.npm_package_version || 'unknown',
                }
            };

            // Save to database
            const messageRepo = this.dataSource.getRepository('ChatAiMessage');
            const message = messageRepo.create(trackingRecord);
            const savedMessage = await messageRepo.save(message);

            // Log summary for monitoring
            this.logTrackingSummary(trackingRecord, savedMessage.id);

            return {
                success: true,
                messageId: savedMessage.id,
                trackingId: `track_${savedMessage.id}_${Date.now()}`
            };

        } catch (error) {
            console.error('❌ Failed to track query interaction:', error.message);
            return {
                success: false,
                reason: 'database_error',
                error: error.message
            };
        }
    }

    /**
     * Log tracking summary for monitoring
     */
    logTrackingSummary(trackingRecord, messageId) {
        const perf = trackingRecord.performanceMetrics;
        const sys = trackingRecord.systemMetrics;
        const ai = trackingRecord.aiMetrics;
        const business = trackingRecord.businessMetrics;

        console.log(`\n📊 ═══════════════════════════════════════════════════════════════`);
        console.log(`📊 QUERY TRACKING SUMMARY - ID: ${messageId}`);
        console.log(`📊 ═══════════════════════════════════════════════════════════════`);
        console.log(`🔢 ChatAI ID: ${trackingRecord.chatAiId}`);
        console.log(`👤 User ID: ${trackingRecord.userId}`);
        console.log(`🔗 Session: ${trackingRecord.sessionId}`);
        console.log(`⏱️ Total Duration: ${perf.totalDuration}ms`);
        console.log(`📄 Documents Used: ${sys.documentsUsed}`);
        console.log(`📏 Context Length: ${sys.contextLength} chars`);
        console.log(`🤖 AI Model: ${ai.modelUsed}`);
        console.log(`💰 Credits: ${business.creditsDeducted} (${business.subscriptionStatus})`);
        console.log(`❌ Errors: ${trackingRecord.errorTracking.errorCount}`);
        console.log(`⚠️ Warnings: ${trackingRecord.errorTracking.warningCount}`);
        console.log(`📊 ═══════════════════════════════════════════════════════════════\n`);
    }

    /**
     * Get system performance analytics
     */
    async getSystemAnalytics(timeRange = '24h', chatAiId = null) {
        if (!this.trackingEnabled || !this.isConnected) {
            return { error: 'tracking_disabled' };
        }

        try {
            const messageRepo = this.dataSource.getRepository('ChatAiMessage');

            // Calculate time range
            const now = new Date();
            const timeRanges = {
                '1h': new Date(now.getTime() - 60 * 60 * 1000),
                '6h': new Date(now.getTime() - 6 * 60 * 60 * 1000),
                '24h': new Date(now.getTime() - 24 * 60 * 60 * 1000),
                '7d': new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
                '30d': new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
            };

            const startTime = timeRanges[timeRange] || timeRanges['24h'];

            // Build query
            const queryBuilder = messageRepo
                .createQueryBuilder('message')
                .where('message.timestamp >= :startTime', { startTime });

            if (chatAiId) {
                queryBuilder.andWhere('message.chatAiId = :chatAiId', { chatAiId });
            }

            const messages = await queryBuilder.getMany();

            // Calculate analytics
            const analytics = this.calculateAnalytics(messages);

            return {
                success: true,
                timeRange,
                chatAiId,
                totalQueries: messages.length,
                analytics
            };

        } catch (error) {
            console.error('❌ Failed to get system analytics:', error.message);
            return { error: error.message };
        }
    }

    /**
     * Calculate comprehensive analytics from tracking data
     */
    calculateAnalytics(messages) {
        if (messages.length === 0) {
            return {
                performance: {},
                usage: {},
                errors: {},
                ai: {},
                business: {}
            };
        }

        const analytics = {
            performance: {
                avgTotalDuration: 0,
                avgApiValidationTime: 0,
                avgVectorSearchTime: 0,
                avgOpenRouterTime: 0,
                p95TotalDuration: 0,
                p99TotalDuration: 0,
                slowestQuery: null,
                fastestQuery: null
            },
            usage: {
                totalDocumentsUsed: 0,
                avgDocumentsPerQuery: 0,
                avgContextLength: 0,
                avgResponseLength: 0,
                avgQueryLength: 0,
                cacheHitRate: 0
            },
            errors: {
                totalErrors: 0,
                errorRate: 0,
                mostCommonErrors: [],
                totalWarnings: 0,
                warningRate: 0
            },
            ai: {
                modelsUsed: {},
                avgTokensUsed: 0,
                totalTokensUsed: 0,
                streamingUsage: 0,
                streamingRate: 0
            },
            business: {
                totalCreditsUsed: 0,
                avgCreditsPerQuery: 0,
                subscriptionBreakdown: {},
                premiumUsage: 0,
                premiumRate: 0
            }
        };

        let totalDuration = 0;
        let totalApiValidation = 0;
        let totalVectorSearch = 0;
        let totalOpenRouter = 0;
        let totalDocuments = 0;
        let totalContextLength = 0;
        let totalResponseLength = 0;
        let totalQueryLength = 0;
        let totalErrors = 0;
        let totalWarnings = 0;
        let totalTokens = 0;
        let streamingCount = 0;
        let totalCredits = 0;
        let premiumCount = 0;

        const durations = [];
        const errorTypes = {};
        const modelUsage = {};
        const subscriptionTypes = {};

        messages.forEach(message => {
            const perf = message.performanceMetrics || {};
            const sys = message.systemMetrics || {};
            const ai = message.aiMetrics || {};
            const business = message.businessMetrics || {};
            const errors = message.errorTracking || {};

            // Performance metrics
            const duration = perf.totalDuration || 0;
            durations.push(duration);
            totalDuration += duration;
            totalApiValidation += perf.apiValidationTime || 0;
            totalVectorSearch += perf.vectorSearchTime || 0;
            totalOpenRouter += perf.openRouterTime || 0;

            // Usage metrics
            totalDocuments += sys.documentsUsed || 0;
            totalContextLength += sys.contextLength || 0;
            totalResponseLength += sys.responseLength || 0;
            totalQueryLength += sys.queryLength || 0;

            // Error tracking
            totalErrors += errors.errorCount || 0;
            totalWarnings += errors.warningCount || 0;

            // AI metrics
            totalTokens += ai.tokensUsed || 0;
            if (ai.streaming) streamingCount++;

            const model = ai.modelUsed || 'unknown';
            modelUsage[model] = (modelUsage[model] || 0) + 1;

            // Business metrics
            totalCredits += business.creditsDeducted || 0;
            const subscription = business.subscriptionStatus || 'unknown';
            subscriptionTypes[subscription] = (subscriptionTypes[subscription] || 0) + 1;

            if (business.isPremium) premiumCount++;
        });

        const count = messages.length;

        // Calculate averages
        analytics.performance.avgTotalDuration = totalDuration / count;
        analytics.performance.avgApiValidationTime = totalApiValidation / count;
        analytics.performance.avgVectorSearchTime = totalVectorSearch / count;
        analytics.performance.avgOpenRouterTime = totalOpenRouter / count;

        analytics.usage.avgDocumentsPerQuery = totalDocuments / count;
        analytics.usage.avgContextLength = totalContextLength / count;
        analytics.usage.avgResponseLength = totalResponseLength / count;
        analytics.usage.avgQueryLength = totalQueryLength / count;

        analytics.ai.avgTokensUsed = totalTokens / count;
        analytics.ai.streamingRate = (streamingCount / count) * 100;

        analytics.business.avgCreditsPerQuery = totalCredits / count;
        analytics.business.premiumRate = (premiumCount / count) * 100;

        // Calculate percentiles
        durations.sort((a, b) => a - b);
        const p95Index = Math.floor(count * 0.95);
        const p99Index = Math.floor(count * 0.99);
        analytics.performance.p95TotalDuration = durations[p95Index] || 0;
        analytics.performance.p99TotalDuration = durations[p99Index] || 0;

        // Set fastest and slowest
        analytics.performance.fastestQuery = durations[0] || 0;
        analytics.performance.slowestQuery = durations[durations.length - 1] || 0;

        // Set breakdowns
        analytics.ai.modelsUsed = modelUsage;
        analytics.business.subscriptionBreakdown = subscriptionTypes;

        // Calculate rates
        analytics.errors.errorRate = (totalErrors / count) * 100;
        analytics.errors.warningRate = (totalWarnings / count) * 100;

        return analytics;
    }

    /**
     * Get real-time system health metrics
     */
    async getSystemHealth() {
        if (!this.trackingEnabled) {
            return { status: 'tracking_disabled' };
        }

        try {
            const now = new Date();
            const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

            const messageRepo = this.dataSource.getRepository('ChatAiMessage');

            // Get recent activity
            const recentMessages = await messageRepo
                .createQueryBuilder('message')
                .where('message.timestamp >= :oneHourAgo', { oneHourAgo })
                .getMany();

            // Calculate health metrics
            const health = {
                status: 'healthy',
                timestamp: now.toISOString(),
                lastHour: {
                    totalQueries: recentMessages.length,
                    avgResponseTime: 0,
                    errorRate: 0,
                    activeSessions: new Set(recentMessages.map(m => m.sessionId)).size,
                    uniqueUsers: new Set(recentMessages.map(m => m.userId)).size
                },
                system: {
                    memoryUsage: process.memoryUsage(),
                    uptime: process.uptime(),
                    nodeVersion: process.version
                }
            };

            if (recentMessages.length > 0) {
                const totalDuration = recentMessages.reduce((sum, m) => {
                    return sum + (m.performanceMetrics?.totalDuration || 0);
                }, 0);
                health.lastHour.avgResponseTime = totalDuration / recentMessages.length;

                const errorCount = recentMessages.reduce((sum, m) => {
                    return sum + (m.errorTracking?.errorCount || 0);
                }, 0);
                health.lastHour.errorRate = (errorCount / recentMessages.length) * 100;

                // Determine overall status
                if (health.lastHour.errorRate > 10) {
                    health.status = 'degraded';
                } else if (health.lastHour.errorRate > 25) {
                    health.status = 'critical';
                }
            }

            return health;

        } catch (error) {
            console.error('❌ Failed to get system health:', error.message);
            return {
                status: 'error',
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Get user behavior analytics
     */
    async getUserAnalytics(userId, timeRange = '7d') {
        if (!this.trackingEnabled || !this.isConnected) {
            return { error: 'tracking_disabled' };
        }

        try {
            const messageRepo = this.dataSource.getRepository('ChatAiMessage');

            const now = new Date();
            const timeRanges = {
                '1d': new Date(now.getTime() - 24 * 60 * 60 * 1000),
                '7d': new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
                '30d': new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
            };

            const startTime = timeRanges[timeRange] || timeRanges['7d'];

            const messages = await messageRepo
                .createQueryBuilder('message')
                .where('message.userId = :userId', { userId })
                .andWhere('message.timestamp >= :startTime', { startTime })
                .orderBy('message.timestamp', 'DESC')
                .getMany();

            const analytics = this.calculateAnalytics(messages);

            return {
                success: true,
                userId,
                timeRange,
                totalQueries: messages.length,
                analytics,
                recentActivity: messages.slice(0, 10).map(m => ({
                    id: m.id,
                    timestamp: m.timestamp,
                    query: m.question?.substring(0, 100),
                    duration: m.performanceMetrics?.totalDuration || 0,
                    documentsUsed: m.systemMetrics?.documentsUsed || 0
                }))
            };

        } catch (error) {
            console.error('❌ Failed to get user analytics:', error.message);
            return { error: error.message };
        }
    }

    /**
     * Export tracking data for analysis
     */
    async exportTrackingData(startDate, endDate, format = 'json') {
        if (!this.trackingEnabled || !this.isConnected) {
            return { error: 'tracking_disabled' };
        }

        try {
            const messageRepo = this.dataSource.getRepository('ChatAiMessage');

            const messages = await messageRepo
                .createQueryBuilder('message')
                .where('message.timestamp >= :startDate', { startDate })
                .andWhere('message.timestamp <= :endDate', { endDate })
                .orderBy('message.timestamp', 'ASC')
                .getMany();

            if (format === 'csv') {
                return this.convertToCSV(messages);
            }

            return {
                success: true,
                format,
                recordCount: messages.length,
                startDate,
                endDate,
                data: messages
            };

        } catch (error) {
            console.error('❌ Failed to export tracking data:', error.message);
            return { error: error.message };
        }
    }

    /**
     * Convert tracking data to CSV format
     */
    convertToCSV(messages) {
        if (messages.length === 0) {
            return { success: true, format: 'csv', data: '' };
        }

        const headers = [
            'ID', 'Timestamp', 'ChatAI ID', 'User ID', 'Session ID',
            'Query', 'Response', 'Total Duration', 'Documents Used',
            'Context Length', 'Response Length', 'Model Used', 'Tokens Used',
            'Credits Deducted', 'Subscription Status', 'Error Count',
            'Warning Count', 'Streaming', 'IP Address', 'User Agent'
        ];

        const csvRows = [headers.join(',')];

        messages.forEach(message => {
            const row = [
                message.id,
                message.timestamp,
                message.chatAiId,
                message.userId,
                message.sessionId,
                `"${(message.question || '').replace(/"/g, '""')}"`,
                `"${(message.response || '').replace(/"/g, '""')}"`,
                message.performanceMetrics?.totalDuration || 0,
                message.systemMetrics?.documentsUsed || 0,
                message.systemMetrics?.contextLength || 0,
                message.systemMetrics?.responseLength || 0,
                message.aiMetrics?.modelUsed || 'unknown',
                message.aiMetrics?.tokensUsed || 0,
                message.businessMetrics?.creditsDeducted || 0,
                message.businessMetrics?.subscriptionStatus || 'unknown',
                message.errorTracking?.errorCount || 0,
                message.errorTracking?.warningCount || 0,
                message.aiMetrics?.streaming || false,
                `"${(message.userBehavior?.ipAddress || '').replace(/"/g, '""')}"`,
                `"${(message.userBehavior?.userAgent || '').replace(/"/g, '""')}"`
            ];
            csvRows.push(row.join(','));
        });

        return {
            success: true,
            format: 'csv',
            recordCount: messages.length,
            data: csvRows.join('\n')
        };
    }

    /**
     * Clean up old tracking data
     */
    async cleanupOldData(retentionDays = 90) {
        if (!this.trackingEnabled || !this.isConnected) {
            return { error: 'tracking_disabled' };
        }

        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

            const messageRepo = this.dataSource.getRepository('ChatAiMessage');

            const result = await messageRepo
                .createQueryBuilder()
                .delete()
                .where('timestamp < :cutoffDate', { cutoffDate })
                .execute();

            console.log(`🧹 Cleaned up ${result.affected} old tracking records (older than ${retentionDays} days)`);

            return {
                success: true,
                deletedRecords: result.affected,
                cutoffDate: cutoffDate.toISOString()
            };

        } catch (error) {
            console.error('❌ Failed to cleanup old tracking data:', error.message);
            return { error: error.message };
        }
    }
}

// Export singleton instance
module.exports = new QueryTrackingService(); 