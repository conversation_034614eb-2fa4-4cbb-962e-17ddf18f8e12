const redisCacheService = require('./redisCacheService');
const redisService = require('./redisService');

// Import original cache service for fallback
const originalCacheService = require('./cacheService');

class HybridCacheService {
  constructor() {
    // Use the singleton instance of original cache service for fallback
    this.fallbackCache = originalCacheService;
    this.redisCache = redisCacheService;

    // Track which backend is being used
    this.usingRedis = false;
    this.lastRedisCheck = 0;
    this.redisCheckInterval = 30000; // Check Redis availability every 30 seconds
  }

  /**
   * Check if Redis is available and update status
   */
  async checkRedisAvailability() {
    const now = Date.now();
    if (now - this.lastRedisCheck < this.redisCheckInterval) {
      return this.usingRedis;
    }

    this.lastRedisCheck = now;
    this.usingRedis = redisService.isAvailable();

    if (this.usingRedis) {
      console.log('✅ Using Redis for caching');
    } else {
      console.log('⚠️ Redis unavailable, using in-memory cache fallback');
    }

    return this.usingRedis;
  }

  /**
   * Execute operation with Redis or fallback
   */
  async executeWithFallback(redisOperation, fallbackOperation) {
    await this.checkRedisAvailability();

    if (this.usingRedis) {
      try {
        return await redisOperation();
      } catch (error) {
        console.error('❌ Redis operation failed, falling back to in-memory:', error.message);
        this.usingRedis = false;
        return fallbackOperation();
      }
    } else {
      return fallbackOperation();
    }
  }

  // ==================== SESSION MANAGEMENT ====================

  /**
   * Cache session data with documents
   */
  async cacheSession(sessionId, appId, documents, authToken) {
    return await this.executeWithFallback(
      () => this.redisCache.cacheSession(sessionId, appId, documents, authToken),
      () => {
        this.fallbackCache.cacheSession(sessionId, appId, documents, authToken);
        return true;
      }
    );
  }

  /**
   * Get cached documents for a session
   */
  async getCachedDocuments(sessionId) {
    return await this.executeWithFallback(
      () => this.redisCache.getCachedDocuments(sessionId),
      () => this.fallbackCache.getCachedDocuments(sessionId)
    );
  }

  /**
   * Get session by app ID
   */
  async getSessionByAppId(appId) {
    return await this.executeWithFallback(
      () => this.redisCache.getSessionByAppId(appId),
      () => this.fallbackCache.getSessionByAppId(appId)
    );
  }

  /**
   * Remove session from cache
   */
  async removeSession(sessionId) {
    return await this.executeWithFallback(
      () => this.redisCache.removeSession(sessionId),
      () => {
        this.fallbackCache.removeSession(sessionId);
        return true;
      }
    );
  }

  // ==================== API KEY VALIDATION ====================

  /**
   * Cache API key validation result
   */
  async cacheApiKeyValidation(apiKey, validationData) {
    return await this.executeWithFallback(
      () => this.redisCache.cacheApiKeyValidation(apiKey, validationData),
      () => {
        this.fallbackCache.cacheApiKeyValidation(apiKey, validationData);
        return true;
      }
    );
  }

  /**
   * Get cached API key validation
   */
  async getCachedApiKeyValidation(apiKey) {
    return await this.executeWithFallback(
      () => this.redisCache.getCachedApiKeyValidation(apiKey),
      () => this.fallbackCache.getCachedApiKeyValidation(apiKey)
    );
  }

  /**
   * Invalidate API key cache
   */
  async invalidateApiKey(apiKey) {
    return await this.executeWithFallback(
      () => this.redisCache.invalidateApiKey(apiKey),
      () => {
        this.fallbackCache.invalidateApiKey(apiKey);
        return true;
      }
    );
  }

  // ==================== CONTEXT CACHE ====================

  /**
   * Cache LlamaIndex context result
   */
  async cacheContext(appId, query, context, documents = []) {
    return await this.executeWithFallback(
      () => this.redisCache.cacheContext(appId, query, context, documents),
      () => {
        this.fallbackCache.cacheContext(appId, query, context, documents);
        return true;
      }
    );
  }

  /**
   * Get cached context
   */
  async getCachedContext(appId, query) {
    return await this.executeWithFallback(
      () => this.redisCache.getCachedContext(appId, query),
      () => this.fallbackCache.getCachedContext(appId, query)
    );
  }

  // ==================== CONVERSATION HISTORY ====================

  /**
   * Add conversation entry
   */
  async addConversationEntry(sessionId, query, response, metadata = {}) {
    return await this.executeWithFallback(
      async () => {
        // For Redis, we'll implement this in the next step
        // For now, fallback to in-memory
        this.fallbackCache.addConversationEntry(sessionId, query, response, metadata);
        return true;
      },
      () => {
        this.fallbackCache.addConversationEntry(sessionId, query, response, metadata);
        return true;
      }
    );
  }

  /**
   * Get conversation history
   */
  async getConversationHistory(sessionId, limit = 10) {
    return await this.executeWithFallback(
      async () => {
        // For Redis, we'll implement this in the next step
        // For now, fallback to in-memory
        return this.fallbackCache.getConversationHistory(sessionId, limit);
      },
      () => this.fallbackCache.getConversationHistory(sessionId, limit)
    );
  }

  // ==================== DOCUMENT CACHE ====================

  /**
   * Cache document content
   */
  async cacheDocument(indexId, documentData) {
    return await this.executeWithFallback(
      async () => {
        // For Redis, we'll implement this in the next step
        // For now, fallback to in-memory
        this.fallbackCache.cacheDocument(indexId, documentData);
        return true;
      },
      () => {
        this.fallbackCache.cacheDocument(indexId, documentData);
        return true;
      }
    );
  }

  /**
   * Get cached document
   */
  async getCachedDocument(indexId) {
    return await this.executeWithFallback(
      async () => {
        // For Redis, we'll implement this in the next step
        // For now, fallback to in-memory
        return this.fallbackCache.getCachedDocument(indexId);
      },
      () => this.fallbackCache.getCachedDocument(indexId)
    );
  }

  // ==================== SESSION UTILITY METHODS ====================

  /**
   * Get or create session for an appId
   */
  getOrCreateSession(appId, sessionId = null) {
    // This method is synchronous in the original, so we use fallback for now
    // TODO: Implement async version for Redis
    return this.fallbackCache.getOrCreateSession(appId, sessionId);
  }

  /**
   * Cache documents for a session (legacy method name)
   */
  async cacheDocuments(sessionId, appId, documents, authToken) {
    return await this.cacheSession(sessionId, appId, documents, authToken);
  }

  /**
   * Get formatted conversation history for LLM consumption
   */
  getFormattedConversationHistory(sessionId) {
    // This method is synchronous in the original, so we use fallback for now
    // TODO: Implement async version for Redis
    return this.fallbackCache.getFormattedConversationHistory(sessionId);
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Update configuration
   */
  updateConfiguration(config) {
    this.redisCache.updateConfiguration(config);
    this.fallbackCache.updateConfiguration(config);
  }

  /**
   * Get cache statistics
   */
  async getStats() {
    await this.checkRedisAvailability();

    if (this.usingRedis) {
      const redisStats = await this.redisCache.getStats();
      const fallbackStats = this.fallbackCache.getStats();

      return {
        backend: 'redis',
        redis: redisStats,
        fallback: fallbackStats,
        note: 'Using Redis as primary cache'
      };
    } else {
      const fallbackStats = this.fallbackCache.getStats();

      return {
        backend: 'memory',
        fallback: fallbackStats,
        redis: { connected: false },
        note: 'Using in-memory cache (Redis unavailable)'
      };
    }
  }

  /**
   * Clear all cache
   */
  async clearAll() {
    const redisResult = await this.executeWithFallback(
      () => this.redisCache.clearAll(),
      () => true
    );

    this.fallbackCache.clearAll();

    return redisResult;
  }

  /**
   * Get current backend being used
   */
  getCurrentBackend() {
    return this.usingRedis ? 'redis' : 'memory';
  }

  /**
   * Force Redis availability check
   */
  async forceRedisCheck() {
    this.lastRedisCheck = 0;
    return await this.checkRedisAvailability();
  }
}

// Export singleton instance
module.exports = new HybridCacheService();
