const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');
const redisService = require('./redisService');

class RedisCacheService {
  constructor() {
    // Cache TTLs in seconds (Redis uses seconds)
    this.ttl = 15 * 60; // 15 minutes default
    this.maxSessions = 1000; // Default
    this.cleanupInterval = 5 * 60; // 5 minutes default

    // Cache TTLs for different types (in seconds)
    this.apiKeyTTL = 10 * 60;    // 10 minutes for API key validation
    this.contextTTL = 30 * 60;   // 30 minutes for query-specific context
    this.documentTTL = 60 * 60;  // 1 hour for document content
    this.semanticTTL = 45 * 60;  // 45 minutes for semantic categories
    this.conversationTTL = 2 * 60 * 60; // 2 hours for conversation

    // Cache key prefixes
    this.prefixes = {
      session: 'session:',
      app: 'app:',
      apiKey: 'apikey:',
      context: 'context:',
      document: 'document:',
      semantic: 'semantic:',
      conversation: 'conversation:'
    };

    // Initialize with defaults, will be updated when config loads
    this.isConfigured = false;
    this.initializeConfig();
  }

  /**
   * Initialize configuration asynchronously
   */
  async initializeConfig() {
    try {
      const configManager = require('../config');

      // Try to get config, but don't fail if it's not ready yet
      if (configManager.isInitialized) {
        const config = configManager.getConfig();
        this.updateConfiguration(config);
      } else {
        // Config will be updated later when it's ready
        console.log('📦 RedisCacheService: Using default values until configuration is loaded');
      }
    } catch (error) {
      console.log('📦 RedisCacheService: Using default configuration values');
    }
  }

  /**
   * Update configuration when it becomes available
   */
  updateConfiguration(config) {
    if (config && config.cache) {
      this.ttl = config.cache.ttlMinutes * 60; // Convert to seconds
      this.maxSessions = config.cache.maxSessions;
      this.cleanupInterval = config.cache.cleanupIntervalMinutes * 60; // Convert to seconds
      this.isConfigured = true;

      console.log('📦 RedisCacheService Configuration Updated:');
      console.log(`   TTL: ${this.ttl / 60} minutes`);
      console.log(`   Max Sessions: ${this.maxSessions}`);
      console.log(`   Cleanup Interval: ${this.cleanupInterval / 60} minutes`);
    }
  }

  /**
   * Check if Redis is available, fallback to in-memory if not
   */
  async isRedisAvailable() {
    return redisService.isAvailable();
  }

  /**
   * Generate cache key with prefix
   */
  getCacheKey(prefix, key) {
    return `${prefix}${key}`;
  }

  // ==================== SESSION MANAGEMENT ====================

  /**
   * Cache session data with documents
   */
  async cacheSession(sessionId, appId, documents, authToken) {
    try {
      const sessionData = {
        sessionId,
        appId,
        documents,
        authToken,
        timestamp: Date.now(),
        lastAccessed: Date.now(),
        accessCount: 1
      };

      // Store session data
      const sessionKey = this.getCacheKey(this.prefixes.session, sessionId);
      await redisService.set(sessionKey, sessionData, this.ttl);

      // Also cache by appId for quick lookup
      const appKey = this.getCacheKey(this.prefixes.app, appId);
      const appData = {
        sessionId,
        timestamp: Date.now()
      };
      await redisService.set(appKey, appData, this.ttl);

      console.log(`💾 Cached ${documents.length} documents for session: ${sessionId} (Redis)`);
      return true;
    } catch (error) {
      console.error('❌ Failed to cache session in Redis:', error.message);
      return false;
    }
  }

  /**
   * Get cached documents for a session
   */
  async getCachedDocuments(sessionId) {
    try {
      const sessionKey = this.getCacheKey(this.prefixes.session, sessionId);
      const session = await redisService.get(sessionKey);

      if (!session) {
        return null;
      }

      // Update last accessed time and increment access count
      session.lastAccessed = Date.now();
      session.accessCount = (session.accessCount || 0) + 1;

      // Update the session in Redis
      await redisService.set(sessionKey, session, this.ttl);

      console.log(`📋 Retrieved cached documents for session: ${sessionId} (access count: ${session.accessCount}) (Redis)`);
      return session;
    } catch (error) {
      console.error('❌ Failed to get cached documents from Redis:', error.message);
      return null;
    }
  }

  /**
   * Get session by app ID
   */
  async getSessionByAppId(appId) {
    try {
      const appKey = this.getCacheKey(this.prefixes.app, appId);
      const appData = await redisService.get(appKey);

      if (!appData) {
        return null;
      }

      // Get the full session data
      return await this.getCachedDocuments(appData.sessionId);
    } catch (error) {
      console.error('❌ Failed to get session by app ID from Redis:', error.message);
      return null;
    }
  }

  /**
   * Remove session from cache
   */
  async removeSession(sessionId) {
    try {
      const sessionKey = this.getCacheKey(this.prefixes.session, sessionId);
      const session = await redisService.get(sessionKey);

      if (session) {
        // Remove app cache entry
        const appKey = this.getCacheKey(this.prefixes.app, session.appId);
        await redisService.del(appKey);
      }

      // Remove session
      await redisService.del(sessionKey);
      console.log(`🗑️ Removed session from cache: ${sessionId} (Redis)`);
      return true;
    } catch (error) {
      console.error('❌ Failed to remove session from Redis:', error.message);
      return false;
    }
  }

  // ==================== API KEY VALIDATION ====================

  /**
   * Cache API key validation result
   */
  async cacheApiKeyValidation(apiKey, validationData) {
    try {
      const hashedKey = this.hashApiKey(apiKey);
      const cacheKey = this.getCacheKey(this.prefixes.apiKey, hashedKey);

      const cacheData = {
        ...validationData,
        timestamp: Date.now(),
        expiresAt: Date.now() + (this.apiKeyTTL * 1000)
      };

      await redisService.set(cacheKey, cacheData, this.apiKeyTTL);
      console.log(`🔑 Cached API key validation: ${hashedKey.substring(0, 8)}... (Redis)`);
      return true;
    } catch (error) {
      console.error('❌ Failed to cache API key validation in Redis:', error.message);
      return false;
    }
  }

  /**
   * Get cached API key validation
   */
  async getCachedApiKeyValidation(apiKey) {
    try {
      const hashedKey = this.hashApiKey(apiKey);
      const cacheKey = this.getCacheKey(this.prefixes.apiKey, hashedKey);
      const cached = await redisService.get(cacheKey);

      if (!cached) {
        console.log(`🔑 API key validation cache miss: ${hashedKey.substring(0, 8)}... (Redis)`);
        return null;
      }

      console.log(`⚡ Using cached API key validation: ${hashedKey.substring(0, 8)}... (Redis)`);
      return cached;
    } catch (error) {
      console.error('❌ Failed to get cached API key validation from Redis:', error.message);
      return null;
    }
  }

  /**
   * Hash API key for secure caching
   */
  hashApiKey(apiKey) {
    return crypto.createHash('sha256').update(apiKey).digest('hex');
  }

  /**
   * Invalidate API key cache
   */
  async invalidateApiKey(apiKey) {
    try {
      const hashedKey = this.hashApiKey(apiKey);
      const cacheKey = this.getCacheKey(this.prefixes.apiKey, hashedKey);
      await redisService.del(cacheKey);
      console.log(`🗑️ Invalidated API key cache: ${hashedKey.substring(0, 8)}... (Redis)`);
      return true;
    } catch (error) {
      console.error('❌ Failed to invalidate API key cache in Redis:', error.message);
      return false;
    }
  }

  // ==================== CONTEXT CACHE ====================

  /**
   * Cache LlamaIndex context result with semantic categorization
   */
  async cacheContext(appId, query, context, documents = []) {
    try {
      const cacheKey = this.generateContextKey(appId, query);
      const semanticCategory = this.categorizeQuery(query);

      const cacheData = {
        context,
        documents,
        query,
        semanticCategory,
        timestamp: Date.now(),
        expiresAt: Date.now() + (this.contextTTL * 1000)
      };

      const contextKey = this.getCacheKey(this.prefixes.context, cacheKey);
      await redisService.set(contextKey, cacheData, this.contextTTL);
      console.log(`🔍 Cached context for appId: ${appId}, category: "${semanticCategory}" (Redis)`);
      return true;
    } catch (error) {
      console.error('❌ Failed to cache context in Redis:', error.message);
      return false;
    }
  }

  /**
   * Get cached context with semantic matching
   */
  async getCachedContext(appId, query) {
    try {
      const cacheKey = this.generateContextKey(appId, query);
      const contextKey = this.getCacheKey(this.prefixes.context, cacheKey);
      const cached = await redisService.get(contextKey);

      if (!cached) {
        const semanticCategory = this.categorizeQuery(query);
        console.log(`🔍 Context cache miss for appId: ${appId}, category: "${semanticCategory}" (Redis)`);
        return null;
      }

      console.log(`⚡ Using cached context for appId: ${appId} (Redis)`);
      return cached;
    } catch (error) {
      console.error('❌ Failed to get cached context from Redis:', error.message);
      return null;
    }
  }

  /**
   * Generate context cache key
   */
  generateContextKey(appId, query) {
    const normalizedQuery = query.toLowerCase().trim();
    const queryHash = crypto.createHash('md5').update(normalizedQuery).digest('hex');
    return `${appId}:${queryHash}`;
  }

  /**
   * Categorize query for semantic caching
   */
  categorizeQuery(query) {
    const lowerQuery = query.toLowerCase();

    // Financial related queries
    if (lowerQuery.match(/\b(amount|total|price|cost|payment|invoice|bill|due|balance)\b/)) {
      return 'financial';
    }

    // Date related queries
    if (lowerQuery.match(/\b(date|time|when|month|year|day|schedule|deadline)\b/)) {
      return 'temporal';
    }

    // Contact/Address related queries
    if (lowerQuery.match(/\b(address|contact|phone|email|location|company|vendor|supplier)\b/)) {
      return 'contact_info';
    }

    // Item/Product related queries
    if (lowerQuery.match(/\b(item|product|service|description|quantity|unit|line)\b/)) {
      return 'item_details';
    }

    // Status related queries
    if (lowerQuery.match(/\b(status|paid|unpaid|pending|complete|approved|rejected)\b/)) {
      return 'status_info';
    }

    // For unique/specific queries, use hash
    return crypto.createHash('md5').update(lowerQuery).digest('hex').substring(0, 8);
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Get cache statistics
   */
  async getStats() {
    try {
      const redisStats = await redisService.getStats();

      if (!redisStats.connected) {
        return {
          redis: redisStats,
          error: 'Redis not available'
        };
      }

      // Get counts for different cache types
      const sessionKeys = await redisService.keys(`${this.prefixes.session}*`);
      const appKeys = await redisService.keys(`${this.prefixes.app}*`);
      const apiKeyKeys = await redisService.keys(`${this.prefixes.apiKey}*`);
      const contextKeys = await redisService.keys(`${this.prefixes.context}*`);

      return {
        redis: redisStats,
        sessions: {
          total: sessionKeys.length,
          maxSessions: this.maxSessions,
          ttlMinutes: this.ttl / 60
        },
        apps: {
          total: appKeys.length
        },
        apiKeys: {
          total: apiKeyKeys.length,
          ttlMinutes: this.apiKeyTTL / 60
        },
        contexts: {
          total: contextKeys.length,
          ttlMinutes: this.contextTTL / 60
        },
        cleanup: {
          intervalMinutes: this.cleanupInterval / 60
        }
      };
    } catch (error) {
      return {
        error: error.message,
        redis: { connected: false }
      };
    }
  }

  /**
   * Clear all cache
   */
  async clearAll() {
    try {
      await redisService.flushPrefix('');
      console.log('🗑️ Cleared all Redis cache');
      return true;
    } catch (error) {
      console.error('❌ Failed to clear Redis cache:', error.message);
      return false;
    }
  }

  /**
   * Clear specific cache type
   */
  async clearCacheType(type) {
    try {
      const prefix = this.prefixes[type];
      if (!prefix) {
        throw new Error(`Unknown cache type: ${type}`);
      }

      await redisService.flushPrefix(prefix);
      console.log(`🗑️ Cleared ${type} cache (Redis)`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to clear ${type} cache in Redis:`, error.message);
      return false;
    }
  }
}

// Export singleton instance
module.exports = new RedisCacheService();
