const fetch = require('node-fetch');

class UserService {
  constructor() {
    // Initialize with fallback values until config is loaded
    this.baseUrl = process.env.USER_SERVICE_URL || 'http://localhost:3000';
    this.chatAiOrigin = process.env.CHATAI_ORIGIN || 'https://chatai.abstraxn.com';

    // Initialize configuration
    this.isConfigured = false;
    this.initializeConfig();
  }

  /**
   * Initialize configuration asynchronously
   */
  async initializeConfig() {
    try {
      const configManager = require('../config');
      const config = await configManager.initialize();
      this.updateConfiguration(config);
    } catch (error) {
      console.warn('⚠️ Failed to initialize UserService config, using fallback values:', error.message);
    }
  }

  /**
   * Get configuration synchronously (returns current config)
   */
  getConfig() {
    return {
      baseUrl: this.baseUrl,
      chatAiOrigin: this.chatAiOrigin,
      isConfigured: this.isConfigured
    };
  }

  /**
   * Update configuration when it becomes available
   */
  updateConfiguration(config) {
    if (config) {
      this.baseUrl = config.userService?.url || process.env.USER_SERVICE_URL || 'http://localhost:3000';
      this.chatAiOrigin = config.chatAiOrigin || process.env.CHATAI_ORIGIN || 'http://localhost:3001';
      this.isConfigured = true;

      console.log('👤 UserService Configuration Updated:');
      console.log(`   Base URL: ${this.baseUrl}`);
      console.log(`   Origin: ${this.chatAiOrigin}`);

      // Validate URLs after configuration update
      this.validateUrls();
    }
  }

  /**
   * Validate that URLs are properly formatted
   */
  validateUrls() {
    const urlPattern = /^https?:\/\/.+/;

    if (!urlPattern.test(this.baseUrl)) {
      console.error(`❌ Invalid USER_SERVICE_URL: "${this.baseUrl}". Must start with http:// or https://`);
      this.baseUrl = 'http://localhost:3000'; // Fallback to safe default
      console.log(`🔄 Falling back to default USER_SERVICE_URL: ${this.baseUrl}`);
    }

    if (!urlPattern.test(this.chatAiOrigin)) {
      console.error(`❌ Invalid CHATAI_ORIGIN: "${this.chatAiOrigin}". Must start with http:// or https://`);
      this.chatAiOrigin = 'http://localhost:3001'; // Fallback to safe default
      console.log(`🔄 Falling back to default CHATAI_ORIGIN: ${this.chatAiOrigin}`);
    }
  }

  /**
   * Validate API key using User-Service key-validator
   * This is the ONLY API call needed - it returns validation + documents in one call
   * @param {string} apiKey - Application API key
   * @param {string} origin - Request origin
   * @returns {Promise<Object>} Validation result with credit info and documents
   */
  async validateApiKey(apiKey, origin) {
    try {
      // Validate URLs before making the request
      this.validateUrls();

      console.log(`\n🔗 ═══════════════════════════════════════════════════════════════`);
      console.log(`🔑 USER SERVICE CALL - API KEY VALIDATION`);
      console.log(`🗝️  API Key: ${apiKey ? `${apiKey.substring(0, 20)}...` : 'Not provided'}`);
      console.log(`🌐 Origin: ${origin}`);
      console.log(`🕐 Timestamp: ${new Date().toISOString()}`);
      console.log(`═══════════════════════════════════════════════════════════════\n`);

      const requestBody = {
        chainId: 1, // Default chainId for ChatAI
        apikey: apiKey,
        origin: origin,
        payload: {
          method: 'validate',
          params: []
        },
        type: 'chatai'
      };

      const fullUrl = `${this.baseUrl}/users/app/key-validator`;
      console.log(`🌐 Making request to: ${fullUrl}`);
      console.log(`📤 Request method: POST`);
      console.log(`📦 Request payload: ${JSON.stringify(requestBody, null, 2)}`);

      // Log request headers that will be sent
      const requestHeaders = {
        'Content-Type': 'application/json',
        'Origin': process.env.CHATAI_ORIGIN
      };
      console.log(`📋 Request headers: ${JSON.stringify(requestHeaders, null, 2)}`);

      // Log additional debugging info
      console.log(`🔍 DEBUG INFO:`);
      console.log(`   🔸 Base URL: ${this.baseUrl}`);
      console.log(`   🔸 ChatAI Origin: ${this.chatAiOrigin}`);
      console.log(`   🔸 API Key length: ${apiKey ? apiKey.length : 0} characters`);
      console.log(`   🔸 Origin parameter: "${origin}"`);
      console.log(`   🔸 Timestamp: ${new Date().toISOString()}`);

      // Additional validation to ensure URL is valid
      if (!fullUrl.startsWith('http://') && !fullUrl.startsWith('https://')) {
        throw new Error(`Invalid URL protocol: "${fullUrl}". URL must start with http:// or https://`);
      }

      const response = await fetch(
        fullUrl,
        {
          method: 'POST',
          headers: requestHeaders,
          body: JSON.stringify(requestBody)
        }
      );

      console.log(`📥 Response status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.log(`❌ USER SERVICE ERROR: ${response.status} - ${errorText}`);
        throw new Error(`Key validation failed: ${response.status} - ${errorText}`);
      }

      const result = await response.json();

      if (result.error) {
        console.log(`❌ USER SERVICE ERROR: ${result.message || 'API key validation failed'}`);
        throw new Error(result.message || 'API key validation failed');
      }

      const credits = result.result.creditInfo?.creditsRemaining || 'Unknown';
      const subscriptionStatus = result.result.creditInfo?.subscriptionStatus || 'Unknown';
      const documentsCount = result.result.documents?.length || 0;

      console.log(`✅ USER SERVICE SUCCESS: API key validated successfully`);
      console.log(`💳 Credits remaining: ${credits}`);
      console.log(`📊 Subscription status: ${subscriptionStatus}`);
      console.log(`📄 Documents included: ${documentsCount} documents`);
      console.log(`⚡ OPTIMIZATION: Got validation + documents in single API call!`);
      console.log(`🔗 ═══════════════════════════════════════════════════════════════\n`);

      return result.result;

    } catch (error) {
      console.error(`❌ USER SERVICE ERROR - API KEY VALIDATION: ${error.message}`);
      console.log(`🔗 ═══════════════════════════════════════════════════════════════\n`);
      throw error;
    }
  }
}

module.exports = new UserService();
