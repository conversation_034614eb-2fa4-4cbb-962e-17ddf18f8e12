const outOfDomainConfig = require('../config/outOfDomainConfig');

/**
 * Out-of-Domain Response Service
 * Provides intelligent responses for queries outside the AI's domain
 */
class OutOfDomainResponseService {
    constructor() {
        // Use configurable response templates
        this.responseTemplates = outOfDomainConfig.responseTemplates;
        this.fallbackResponse = outOfDomainConfig.fallbackResponse;
    }

    /**
     * Generate appropriate response for out-of-domain query
     * @param {string} query - Original user query
     * @param {Object} outOfDomainResult - Out-of-domain detection result
     * @param {Object} semanticResult - Semantic analysis result
     * @param {Object} aiContext - AI domain and role context
     * @returns {Object} Response object with message and suggestions
     */
    generateResponse(query, outOfDomainResult, semanticResult, aiContext = null) {
        // Get base response template for the detected category
        const baseResponse = this.getResponseTemplate(outOfDomainResult.matchedPatterns);

        // Make response domain-aware if AI context is provided
        const domainAwareResponse = aiContext ?
            this.makeDomainAware(baseResponse, aiContext, outOfDomainResult.matchedPatterns) :
            baseResponse;

        // Add context-specific suggestions
        const contextualSuggestions = this.generateContextualSuggestions(query, outOfDomainResult, aiContext);

        return {
            message: domainAwareResponse.message,
            suggestions: [...domainAwareResponse.suggestions, ...contextualSuggestions],
            confidence: outOfDomainResult.confidence,
            reasoning: outOfDomainResult.reasoning,
            query_intent: outOfDomainResult.queryIntent,
            semantic_score: semanticResult.semanticScore,
            ai_domain: aiContext?.ai_domain || 'unknown',
            ai_role: aiContext?.ai_role || 'unknown'
        };
    }

    /**
     * Get appropriate response template based on matched patterns
     * @param {Array} matchedPatterns - Array of matched out-of-domain patterns
     * @returns {Object} Response template
     */
    getResponseTemplate(matchedPatterns) {
        if (!matchedPatterns || matchedPatterns.length === 0) {
            return this.fallbackResponse;
        }

        // Return the first matched pattern's template
        const primaryPattern = matchedPatterns[0];
        return this.responseTemplates[primaryPattern] || this.fallbackResponse;
    }

    /**
     * Make response domain-aware based on AI context
     * @param {Object} baseResponse - Base response template
     * @param {Object} aiContext - AI domain and role context
     * @param {Array} matchedPatterns - Matched out-of-domain patterns
     * @returns {Object} Domain-aware response
     */
    makeDomainAware(baseResponse, aiContext, matchedPatterns) {
        const { ai_domain, ai_role } = aiContext;

        // Define domain-specific terminology
        const domainTerminology = {
            'hr_policies': {
                name: 'HR policies and workplace guidance',
                examples: ['travel policy', 'expense claims', 'workplace procedures'],
                topics: ['company policies', 'workplace guidelines', 'employee procedures']
            },
            'technical': {
                name: 'technical documentation and guides',
                examples: ['API documentation', 'technical guides', 'system procedures'],
                topics: ['technical documentation', 'system guides', 'API references']
            },
            'documentation': {
                name: 'documentation and user guides',
                examples: ['user guides', 'documentation', 'help articles'],
                topics: ['documentation', 'user guides', 'help resources']
            },
            'legal': {
                name: 'legal documents and compliance',
                examples: ['legal policies', 'compliance guidelines', 'regulatory documents'],
                topics: ['legal documents', 'compliance policies', 'regulatory guidelines']
            },
            'medical': {
                name: 'medical information and healthcare guidance',
                examples: ['medical procedures', 'healthcare policies', 'treatment guidelines'],
                topics: ['medical information', 'healthcare guidance', 'treatment procedures']
            },
            'financial': {
                name: 'financial policies and procedures',
                examples: ['financial policies', 'budget guidelines', 'expense procedures'],
                topics: ['financial policies', 'budget procedures', 'expense guidelines']
            },
            'general': {
                name: 'general information and guidance',
                examples: ['general information', 'guidance documents', 'help resources'],
                topics: ['available information', 'guidance documents', 'help resources']
            }
        };

        const domainInfo = domainTerminology[ai_domain] || domainTerminology['general'];

        // Create domain-aware response
        const domainAwareResponse = {
            message: baseResponse.message,
            suggestions: []
        };

        // Customize message for certain categories that mention specialization
        if (matchedPatterns.includes('general_knowledge') ||
            matchedPatterns.includes('technology') ||
            matchedPatterns.includes('medical')) {

            domainAwareResponse.message = domainAwareResponse.message.replace(
                /HR policies and workplace guidance/gi,
                domainInfo.name
            ).replace(
                /HR policies/gi,
                domainInfo.name
            );
        }

        // Generate domain-aware suggestions
        domainAwareResponse.suggestions = [
            `Ask about ${domainInfo.topics[0]} or ${domainInfo.topics[1]}`,
            `I can help with questions like: ${domainInfo.examples.slice(0, 2).map(ex => `"${ex}"`).join(' or ')}`
        ];

        return domainAwareResponse;
    }

    /**
     * Make response domain-aware based on AI context
     * @param {Object} baseResponse - Base response template
     * @param {Object} aiContext - AI domain and role context
     * @param {Array} matchedPatterns - Matched out-of-domain patterns
     * @returns {Object} Domain-aware response
     */
    makeDomainAware(baseResponse, aiContext, matchedPatterns) {
        const { ai_domain, ai_role } = aiContext;

        // Define domain-specific terminology
        const domainTerminology = {
            'hr_policies': {
                name: 'HR policies and workplace guidance',
                examples: ['travel policy', 'expense claims', 'workplace procedures'],
                topics: ['company policies', 'workplace guidelines', 'employee procedures']
            },
            'technical': {
                name: 'technical documentation and guides',
                examples: ['API documentation', 'technical guides', 'system procedures'],
                topics: ['technical documentation', 'system guides', 'API references']
            },
            'documentation': {
                name: 'documentation and user guides',
                examples: ['user guides', 'documentation', 'help articles'],
                topics: ['documentation', 'user guides', 'help resources']
            },
            'legal': {
                name: 'legal documents and compliance',
                examples: ['legal policies', 'compliance guidelines', 'regulatory documents'],
                topics: ['legal documents', 'compliance policies', 'regulatory guidelines']
            },
            'medical': {
                name: 'medical information and healthcare guidance',
                examples: ['medical procedures', 'healthcare policies', 'treatment guidelines'],
                topics: ['medical information', 'healthcare guidance', 'treatment procedures']
            },
            'financial': {
                name: 'financial policies and procedures',
                examples: ['financial policies', 'budget guidelines', 'expense procedures'],
                topics: ['financial policies', 'budget procedures', 'expense guidelines']
            },
            'general': {
                name: 'general information and guidance',
                examples: ['general information', 'guidance documents', 'help resources'],
                topics: ['available information', 'guidance documents', 'help resources']
            }
        };

        const domainInfo = domainTerminology[ai_domain] || domainTerminology['general'];

        // Create domain-aware response
        const domainAwareResponse = {
            message: baseResponse.message,
            suggestions: []
        };

        // Customize message for certain categories that mention specialization
        if (matchedPatterns.includes('general_knowledge') ||
            matchedPatterns.includes('technology') ||
            matchedPatterns.includes('medical')) {

            domainAwareResponse.message = domainAwareResponse.message.replace(
                /HR policies and workplace guidance/gi,
                domainInfo.name
            ).replace(
                /HR policies/gi,
                domainInfo.name
            );
        }

        // Generate domain-aware suggestions
        domainAwareResponse.suggestions = [
            `Ask about ${domainInfo.topics[0]} or ${domainInfo.topics[1]}`,
            `I can help with questions like: ${domainInfo.examples.slice(0, 2).map(ex => `"${ex}"`).join(' or ')}`
        ];

        return domainAwareResponse;
    }

    /**
     * Generate contextual suggestions based on query content
     * @param {string} query - Original query
     * @param {Object} outOfDomainResult - Out-of-domain detection result
     * @param {Object} aiContext - AI domain and role context
     * @returns {Array} Array of contextual suggestions
     */
    generateContextualSuggestions(query, outOfDomainResult, aiContext = null) {
        const suggestions = [];
        const queryLower = query.toLowerCase();
        const aiDomain = aiContext?.ai_domain || 'general';

        // Domain-specific contextual suggestions
        if (aiDomain === 'hr_policies') {
            // HR-specific suggestions
            if (queryLower.includes('travel') || queryLower.includes('trip')) {
                suggestions.push("Ask about company travel policies or expense reimbursement");
            }
            if (queryLower.includes('money') || queryLower.includes('cost') || queryLower.includes('pay')) {
                suggestions.push("Try asking about expense policies or allowances");
            }
            if (queryLower.includes('work') || queryLower.includes('office')) {
                suggestions.push("Ask about workplace policies or employee guidelines");
            }
        } else if (aiDomain === 'technical') {
            // Technical documentation suggestions
            if (queryLower.includes('api') || queryLower.includes('code')) {
                suggestions.push("Ask about API documentation or technical guides");
            }
            if (queryLower.includes('system') || queryLower.includes('software')) {
                suggestions.push("Try asking about system documentation or user guides");
            }
        } else if (aiDomain === 'legal') {
            // Legal documentation suggestions
            if (queryLower.includes('law') || queryLower.includes('legal')) {
                suggestions.push("Ask about legal policies or compliance guidelines");
            }
            if (queryLower.includes('contract') || queryLower.includes('agreement')) {
                suggestions.push("Try asking about contract templates or legal procedures");
            }
        } else if (aiDomain === 'medical') {
            // Medical documentation suggestions
            if (queryLower.includes('health') || queryLower.includes('medical')) {
                suggestions.push("Ask about medical procedures or healthcare policies");
            }
            if (queryLower.includes('treatment') || queryLower.includes('procedure')) {
                suggestions.push("Try asking about treatment guidelines or medical protocols");
            }
        }

        // If no specific suggestions, add domain-appropriate general ones
        if (suggestions.length === 0) {
            const domainFocus = {
                'hr_policies': 'workplace policies',
                'technical': 'technical documentation',
                'legal': 'legal documents',
                'medical': 'medical information',
                'financial': 'financial procedures',
                'documentation': 'available documentation',
                'general': 'available topics'
            };

            suggestions.push(`Try rephrasing your question to focus on ${domainFocus[aiDomain] || 'available topics'}`);
        }

        return suggestions;
    }

    /**
     * Format response for API output
     * @param {Object} response - Response object from generateResponse
     * @returns {Object} Formatted API response
     */
    formatForAPI(response) {
        return {
            error: true,
            message: response.message,
            suggestions: response.suggestions,
            metadata: {
                out_of_domain: true,
                confidence: response.confidence,
                reasoning: response.reasoning,
                query_intent: response.query_intent,
                semantic_score: response.semantic_score
            }
        };
    }

    /**
     * Generate validation errors for guardrails
     * @param {Object} response - Response object from generateResponse
     * @returns {Array} Array of validation error messages
     */
    generateValidationErrors(response) {
        const errors = [response.message];

        // Add top suggestions as additional context
        if (response.suggestions && response.suggestions.length > 0) {
            errors.push(...response.suggestions.slice(0, 2)); // Limit to top 2 suggestions
        }

        return errors;
    }

    /**
     * Check if query might be a misunderstood in-domain query
     * @param {string} query - Original query
     * @param {Object} outOfDomainResult - Out-of-domain detection result
     * @returns {boolean} True if might be misunderstood in-domain query
     */
    mightBeMisunderstood(query, outOfDomainResult) {
        if (!outOfDomainConfig.misunderstoodQuerySettings.enabled) {
            return false;
        }

        const queryLower = query.toLowerCase();

        // Use configurable potential HR terms
        const potentialHRTerms = outOfDomainConfig.misunderstoodQuerySettings.potentialHRTerms;

        const hasHRTerms = potentialHRTerms.some(term => queryLower.includes(term));
        const lowConfidence = outOfDomainResult.confidence < outOfDomainConfig.thresholds.misunderstoodThreshold;

        return hasHRTerms && lowConfidence;
    }

    /**
     * Generate alternative query suggestions
     * @param {string} originalQuery - Original user query
     * @returns {Array} Array of alternative query suggestions
     */
    generateAlternativeQueries(originalQuery) {
        const alternatives = [];
        const queryLower = originalQuery.toLowerCase();

        // Generate alternatives based on query content
        if (queryLower.includes('travel')) {
            alternatives.push("What is the company travel policy?");
            alternatives.push("How do I book business travel?");
        }

        if (queryLower.includes('expense') || queryLower.includes('money')) {
            alternatives.push("How do I submit expense claims?");
            alternatives.push("What expenses can I claim?");
        }

        if (queryLower.includes('hotel') || queryLower.includes('accommodation')) {
            alternatives.push("What is the hotel booking policy?");
            alternatives.push("What are the lodging allowances?");
        }

        // Default alternatives if none specific
        if (alternatives.length === 0) {
            alternatives.push("What are the travel policies?");
            alternatives.push("How do I claim expenses?");
            alternatives.push("What are the workplace guidelines?");
        }

        return alternatives;
    }
}

module.exports = new OutOfDomainResponseService();
