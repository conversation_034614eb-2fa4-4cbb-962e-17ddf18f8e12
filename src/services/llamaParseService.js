const fetch = require('node-fetch');
const FormData = require('form-data');

/**
 * LlamaParse Service for document parsing and text extraction
 * Moved from User-Service for better separation of concerns
 */
class LlamaParseService {
  constructor() {
    // Use the working API key
    this.apiKey = process.env.LLAMA_CLOUD_API_KEY || 'llx-TbU8YjpDLXfbJ4lwYwbDJYp5DKllwMIcGfB3SGJwGJ7pvtCp';
    this.baseUrl = 'https://api.cloud.llamaindex.ai/api/v1';
    this.isConfigured = !!this.apiKey;

    if (!this.apiKey) {
      // console.warn('⚠️  LLAMA_CLOUD_API_KEY not configured. Document parsing will be disabled.');
    } else {
      // console.log('✅ LlamaParseService initialized successfully');
    }
  }

  checkConfiguration() {
    if (!this.isConfigured) {
      throw new Error('LlamaParse service is not configured. Please set LLAMA_CLOUD_API_KEY.');
    }
  }

  /**
   * Parse file using LlamaParse API with retry logic
   */
  async parseFile(fileBuffer, filename) {
    this.checkConfiguration();

    const maxRetries = 3;
    let attempt = 0;

    while (attempt <= maxRetries) {
      try {
        // console.log(`📄 Starting to parse file: ${filename}${attempt > 0 ? ` (attempt ${attempt + 1}/${maxRetries + 1})` : ''}`);

        // Upload file and get job ID with retry logic
        const jobId = await this.uploadFileWithRetry(fileBuffer, filename, attempt);

        // console.log(`🔄 File uploaded, job ID: ${jobId}. Waiting for processing...`);

        // Poll for completion with retry logic
        return await this.pollForCompletion(jobId, filename);

      } catch (error) {
        const isRetryableError = this.isRetryableError(error);

        if (attempt < maxRetries && isRetryableError) {
          const retryDelay = this.calculateRetryDelay(attempt, error);
          console.log(`⚠️ Attempt ${attempt + 1} failed: ${error.message}`);
          console.log(`🔄 Retrying in ${retryDelay / 1000} seconds... (${maxRetries - attempt} attempts remaining)`);

          await new Promise(resolve => setTimeout(resolve, retryDelay));
          attempt++;
          continue;
        } else {
          // Max retries exceeded or non-retryable error
          console.error(`❌ LlamaParse error for ${filename} after ${attempt + 1} attempts:`, error.message);
          throw error;
        }
      }
    }
  }

  /**
   * Check if an error is retryable
   */
  isRetryableError(error) {
    const errorMessage = error.message.toLowerCase();

    // Network-related errors that should be retried
    const retryablePatterns = [
      'eai_again',           // DNS resolution failure
      'enotfound',           // DNS lookup failed
      'econnreset',          // Connection reset
      'econnrefused',        // Connection refused
      'etimedout',           // Request timeout
      'timeout',             // General timeout
      'network',             // Network error
      'fetch failed',        // Fetch failure
      'socket hang up',      // Socket error
      'getaddrinfo',         // DNS resolution error
      'status check failed', // Status check network error
      'result fetch failed', // Result fetch network error
      'upload failed: 5',    // 5xx server errors
      'parsing failed: network', // Network-related parsing failures
    ];

    // Check if the error message contains any of the retryable patterns
    const messageContainsPattern = retryablePatterns.some(pattern => errorMessage.includes(pattern));

    // Also check error.code and error.errno properties for network errors
    // This handles cases where the error message doesn't contain the pattern
    // but the error code does (e.g., ECONNREFUSED)
    const hasRetryableCode = error.code &&
      ['ECONNREFUSED', 'ECONNRESET', 'ETIMEDOUT', 'EAI_AGAIN', 'ENOTFOUND'].includes(error.code);

    // Log the error details for debugging
    if (hasRetryableCode) {
      console.log(`⚠️ Detected retryable error code: ${error.code}`);
    }

    return messageContainsPattern || hasRetryableCode;
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  calculateRetryDelay(attempt, error) {
    const baseDelay = 2000; // 2 seconds base delay
    const maxDelay = 30000; // 30 seconds max delay

    // Exponential backoff: 2s, 4s, 8s, etc.
    let delay = baseDelay * Math.pow(2, attempt);

    // Add jitter to prevent thundering herd
    delay += Math.random() * 1000;

    // Cap at max delay
    delay = Math.min(delay, maxDelay);

    // Special handling for specific error types
    const errorMessage = error.message.toLowerCase();
    if (errorMessage.includes('rate limit') || errorMessage.includes('429')) {
      delay = Math.max(delay, 60000); // At least 60 seconds for rate limits
    } else if (errorMessage.includes('eai_again') || errorMessage.includes('getaddrinfo')) {
      delay = Math.max(delay, 5000); // At least 5 seconds for DNS issues
    }

    return delay;
  }

  /**
   * Upload file with retry logic
   */
  async uploadFileWithRetry(fileBuffer, filename, attempt) {
    // Create FormData for file upload
    const formData = new FormData();
    formData.append('file', fileBuffer, {
      filename: filename,
      contentType: this.getContentType(filename)
    });

    // Upload file and get job ID
    const uploadResponse = await fetch(`${this.baseUrl}/parsing/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        ...formData.getHeaders()
      },
      body: formData,
    });

    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();
      console.error(`❌ LlamaParse upload error: ${uploadResponse.status} - ${errorText}`);
      throw new Error(`Upload failed: ${uploadResponse.status} - ${errorText}`);
    }

    const uploadResult = await uploadResponse.json();
    const jobId = uploadResult.id;

    if (!jobId) {
      throw new Error('Failed to get job ID from upload response');
    }

    return jobId;
  }

  /**
   * Poll for parsing completion with retry logic
   */
  async pollForCompletion(jobId, filename) {
    let attempts = 0;
    const maxAttempts = 60; // 5 minutes max (5 second intervals)

    while (attempts < maxAttempts) {
      try {
        const statusResponse = await this.getParseStatusWithRetry(jobId);
        console.log(`📊 Job ${jobId} status check ${attempts + 1}: ${statusResponse.status}`);

        if (statusResponse.status === 'SUCCESS') {
          // Get the result in JSON format (includes pages array)
          const result = await this.getParseResultWithRetry(jobId);
          console.log(`✅ Parsing completed for job ${jobId}`);

          // Process the JSON result which includes pages array
          if (!result.pages || !Array.isArray(result.pages)) {
            throw new Error('Invalid result format: missing pages array');
          }

          const pages = result.pages;
          const pageCount = pages.length;
          console.log(`📄 Document has ${pageCount} pages`);

          // Extract full text for backward compatibility
          const fullText = pages
            .map(page => page.md || page.text || '')
            .join('\n\n');

          if (!fullText.trim()) {
            throw new Error('No parsable text found in any page');
          }

          // Return both page-based and full text data
          return {
            id: jobId,
            status: 'SUCCESS',
            result: {
              // Full text for backward compatibility
              text: fullText,
              // Page-based data for improved chunking
              pages: pages.map((page, index) => ({
                pageNumber: page.page || index + 1,
                text: page.text || '',
                markdown: page.md || '',
                images: page.images || [],
                items: page.items || [],
                wordCount: this.countWords(page.text || page.md || '')
              })),
              metadata: {
                page_count: pageCount,
                word_count: this.countWords(fullText),
                filename: filename,
                job_metadata: result.job_metadata || {}
              },
            },
          };
        } else if (statusResponse.status === 'ERROR' || statusResponse.status === 'FAILED') {
          const errorMessage = statusResponse.error || statusResponse.message || 'Parsing failed';
          // console.error(`❌ Job ${jobId} failed: ${errorMessage}`);
          throw new Error(`Parsing failed: ${errorMessage}`);
        }

        // Wait 5 seconds before checking again
        await new Promise(resolve => setTimeout(resolve, 5000));
        attempts++;
      } catch (error) {
        if (this.isRetryableError(error) && attempts < maxAttempts - 1) {
          // console.log(`⚠️ Status check failed, retrying: ${error.message}`);
          attempts++;
          await new Promise(resolve => setTimeout(resolve, 5000));
        } else {
          throw error;
        }
      }
    }

    throw new Error('Parsing timeout - job took too long to complete');
  }

  /**
   * Get parsing job status with retry logic
   */
  async getParseStatusWithRetry(jobId) {
    const maxRetries = 2;
    let attempt = 0;

    while (attempt <= maxRetries) {
      try {
        const response = await fetch(`${this.baseUrl}/parsing/job/${jobId}`, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Status check failed: ${response.status} - ${errorText}`);
        }

        return await response.json();
      } catch (error) {
        if (attempt < maxRetries && this.isRetryableError(error)) {
          const delay = 2000 * (attempt + 1); // 2s, 4s
          console.log(`⚠️ Status check retry ${attempt + 1}/${maxRetries} in ${delay / 1000}s: ${error.message}`);
          await new Promise(resolve => setTimeout(resolve, delay));
          attempt++;
        } else {
          throw error;
        }
      }
    }
  }

  /**
   * Get parsing job status (legacy method for backward compatibility)
   */
  async getParseStatus(jobId) {
    return this.getParseStatusWithRetry(jobId);
  }

  /**
   * Get parsing job result in JSON format with retry logic
   */
  async getParseResultWithRetry(jobId) {
    const maxRetries = 2;
    let attempt = 0;

    while (attempt <= maxRetries) {
      try {
        const response = await fetch(`${this.baseUrl}/parsing/job/${jobId}/result/json`, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Result fetch failed: ${response.status} - ${errorText}`);
        }

        return await response.json();
      } catch (error) {
        if (attempt < maxRetries && this.isRetryableError(error)) {
          const delay = 2000 * (attempt + 1); // 2s, 4s
          console.log(`⚠️ Result fetch retry ${attempt + 1}/${maxRetries} in ${delay / 1000}s: ${error.message}`);
          await new Promise(resolve => setTimeout(resolve, delay));
          attempt++;
        } else {
          throw error;
        }
      }
    }
  }

  /**
   * Get parsing job result in JSON format (legacy method for backward compatibility)
   */
  async getParseResult(jobId) {
    return this.getParseResultWithRetry(jobId);
  }

  /**
   * Count words in text
   */
  countWords(text) {
    return text.split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Get content type based on file extension
   */
  getContentType(filename) {
    const ext = filename.toLowerCase().split('.').pop();
    const contentTypes = {
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      txt: 'text/plain',
      md: 'text/markdown',
      html: 'text/html',
      rtf: 'application/rtf',
    };

    return contentTypes[ext] || 'application/octet-stream';
  }

  /**
   * Parse text content directly (for already extracted text)
   */
  async parseTextContent(text, filename = 'text_content.txt') {
    console.log(`📄 Processing text content directly: ${filename}`);

    const wordCount = this.countWords(text);

    return {
      id: `text_${Date.now()}`,
      status: 'SUCCESS',
      result: {
        text: text,
        metadata: {
          page_count: 1,
          word_count: wordCount,
          filename: filename,
        },
      },
    };
  }
}

module.exports = new LlamaParseService();
