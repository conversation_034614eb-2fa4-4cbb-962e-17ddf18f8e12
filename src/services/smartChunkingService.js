const logger = require('../utils/logger');

/**
 * Smart Chunking Service for optimal content organization
 * Creates larger, more meaningful chunks for better semantic search
 */
class SmartChunkingService {
  constructor() {
    this.config = {
      // Target chunk sizes (in words) - configurable via environment
      targetChunkSize: parseInt(process.env.TARGET_CHUNK_SIZE) || 1000,
      minChunkSize: parseInt(process.env.MIN_CHUNK_SIZE) || 500,
      maxChunkSize: parseInt(process.env.MAX_CHUNK_SIZE) || 1500,

      // Overlap settings
      overlapPercent: parseFloat(process.env.CHUNK_OVERLAP_PERCENT) || 0.1,
      minOverlapWords: 50,          // Minimum overlap in words

      // Quality thresholds
      minMeaningfulRatio: 0.6,      // Minimum meaningful content ratio
      maxRepetitionRatio: 0.3,      // Maximum repetition allowed

      // Merging settings
      maxMergeDistance: 3,          // Max pages to look ahead for merging
      semanticMergeThreshold: 0.7,  // Similarity threshold for merging

      // Feature flags
      enabled: process.env.SMART_CHUNKING_ENABLED !== 'false', // Default enabled
    };

    console.log('🧠 Smart Chunking Configuration:');
    console.log(`   Enabled: ${this.config.enabled}`);
    console.log(`   Target chunk size: ${this.config.targetChunkSize} words`);
    console.log(`   Size range: ${this.config.minChunkSize}-${this.config.maxChunkSize} words`);
    console.log(`   Overlap: ${Math.round(this.config.overlapPercent * 100)}%`);
  }

  /**
   * Create optimized chunks from filtered pages
   * @param {Array} pages - Filtered pages from document processing
   * @param {Object} documentInfo - Document metadata
   * @returns {Object} Optimized chunks and metadata
   */
  createOptimizedChunks(pages, documentInfo = {}) {
    console.log('\n🧠 Smart Chunking: Creating optimized content chunks');
    console.log('==================================================');

    const startTime = Date.now();
    const originalChunkCount = pages.length;

    // Step 1: Analyze page structure and content
    const analyzedPages = this.analyzePageStructure(pages);

    // Step 2: Group related pages into semantic sections
    const semanticSections = this.groupIntoSemanticSections(analyzedPages);

    // Step 3: Create optimized chunks from sections
    const optimizedChunks = this.createChunksFromSections(semanticSections);

    // Step 4: Apply final optimization
    const finalChunks = this.applyFinalOptimization(optimizedChunks);

    const processingTime = Date.now() - startTime;

    // Generate comprehensive report
    const chunkingReport = this.generateChunkingReport(
      originalChunkCount,
      finalChunks,
      processingTime,
      documentInfo
    );

    console.log('\n📊 Smart Chunking Results:');
    console.log(`   📄 Original pages: ${originalChunkCount}`);
    console.log(`   🧠 Optimized chunks: ${finalChunks.length}`);
    console.log(`   📈 Reduction: ${Math.round((1 - finalChunks.length / originalChunkCount) * 100)}%`);
    console.log(`   📝 Avg chunk size: ${Math.round(finalChunks.reduce((sum, c) => sum + c.wordCount, 0) / finalChunks.length)} words`);
    console.log(`   ⏱️  Processing time: ${processingTime}ms`);

    return {
      chunks: finalChunks,
      metadata: this.createOptimizedMetadata(finalChunks, documentInfo),
      report: chunkingReport
    };
  }

  /**
   * Analyze page structure and content quality
   */
  analyzePageStructure(pages) {
    return pages.map((page, index) => {
      const text = page.text || page.markdown || '';
      const wordCount = this.countWords(text);
      const charCount = text.length;

      // Detect content type
      const contentType = this.detectContentType(text);

      // Calculate quality metrics
      const qualityScore = this.calculateQualityScore(text);

      // Detect section boundaries
      const hasSectionHeader = this.detectSectionHeader(text);
      const hasListContent = this.detectListContent(text);

      return {
        ...page,
        index,
        text,
        wordCount,
        charCount,
        contentType,
        qualityScore,
        hasSectionHeader,
        hasListContent,
        mergeable: wordCount < this.config.minChunkSize && qualityScore.meaningfulRatio > 0.5
      };
    });
  }

  /**
   * Group pages into semantic sections
   */
  groupIntoSemanticSections(analyzedPages) {
    const sections = [];
    let currentSection = null;

    for (let i = 0; i < analyzedPages.length; i++) {
      const page = analyzedPages[i];

      // Start new section if:
      // 1. This is a section header
      // 2. Current section is large enough
      // 3. Content type changes significantly
      if (this.shouldStartNewSection(page, currentSection)) {
        if (currentSection) {
          sections.push(currentSection);
        }
        currentSection = {
          pages: [page],
          startPage: page.pageNumber || i + 1,
          contentType: page.contentType,
          totalWords: page.wordCount,
          totalChars: page.charCount
        };
      } else {
        // Add to current section
        if (currentSection) {
          currentSection.pages.push(page);
          currentSection.totalWords += page.wordCount;
          currentSection.totalChars += page.charCount;
          currentSection.endPage = page.pageNumber || i + 1;
        } else {
          // First page
          currentSection = {
            pages: [page],
            startPage: page.pageNumber || i + 1,
            contentType: page.contentType,
            totalWords: page.wordCount,
            totalChars: page.charCount
          };
        }
      }
    }

    // Add final section
    if (currentSection) {
      sections.push(currentSection);
    }

    console.log(`🔗 Grouped ${analyzedPages.length} pages into ${sections.length} semantic sections`);
    return sections;
  }

  /**
   * Create optimized chunks from semantic sections
   */
  createChunksFromSections(sections) {
    const chunks = [];

    for (const section of sections) {
      if (section.totalWords <= this.config.maxChunkSize) {
        // Section fits in one chunk
        chunks.push(this.createChunkFromSection(section));
      } else {
        // Split large section into multiple chunks with overlap
        const sectionChunks = this.splitLargeSection(section);
        chunks.push(...sectionChunks);
      }
    }

    return chunks;
  }

  /**
   * Create a single chunk from a section
   */
  createChunkFromSection(section) {
    const combinedText = section.pages.map(p => p.text).join('\n\n');
    const wordCount = this.countWords(combinedText);

    return {
      text: combinedText,
      wordCount: wordCount,
      charCount: combinedText.length,
      pageRange: {
        start: section.startPage,
        end: section.endPage || section.startPage
      },
      pageCount: section.pages.length,
      contentType: section.contentType,
      qualityScore: this.calculateQualityScore(combinedText),
      sourcePages: section.pages.map(p => ({
        pageNumber: p.pageNumber,
        originalIndex: p.index,
        wordCount: p.wordCount
      }))
    };
  }

  /**
   * Split large section into overlapping chunks
   */
  splitLargeSection(section) {
    const chunks = [];
    const pages = section.pages;
    let currentChunk = [];
    let currentWordCount = 0;

    for (let i = 0; i < pages.length; i++) {
      const page = pages[i];

      // Check if adding this page would exceed max size
      if (currentWordCount + page.wordCount > this.config.maxChunkSize && currentChunk.length > 0) {
        // Create chunk from current pages
        chunks.push(this.createChunkFromPages(currentChunk));

        // Start new chunk with overlap
        const overlapPages = this.calculateOverlapPages(currentChunk);
        currentChunk = [...overlapPages, page];
        currentWordCount = overlapPages.reduce((sum, p) => sum + p.wordCount, 0) + page.wordCount;
      } else {
        currentChunk.push(page);
        currentWordCount += page.wordCount;
      }
    }

    // Add final chunk
    if (currentChunk.length > 0) {
      chunks.push(this.createChunkFromPages(currentChunk));
    }

    return chunks;
  }

  /**
   * Helper methods
   */
  shouldStartNewSection(page, currentSection) {
    if (!currentSection) return true;
    if (page.hasSectionHeader) return true;
    if (currentSection.totalWords >= this.config.targetChunkSize) return true;
    if (page.contentType !== currentSection.contentType) return true;
    return false;
  }

  detectContentType(text) {
    const lowerText = text.toLowerCase();

    if (/^(chapter|section|part|\d+\.|\#)/i.test(text.trim())) return 'header';
    if (text.includes('•') || text.includes('-') || /^\s*\d+\./m.test(text)) return 'list';
    if (text.includes('|') && text.includes('-')) return 'table';
    if (/figure|fig\.|table|chart/i.test(text)) return 'figure';
    if (text.length < 100) return 'short';
    return 'content';
  }

  detectSectionHeader(text) {
    return /^(chapter|section|part|\d+\.|\#)/i.test(text.trim());
  }

  detectListContent(text) {
    return text.includes('•') || text.includes('-') || /^\s*\d+\./m.test(text);
  }

  calculateQualityScore(text) {
    const charCount = text.length;
    const wordCount = this.countWords(text);
    const meaningfulChars = (text.match(/[a-zA-Z0-9]/g) || []).length;
    const meaningfulRatio = meaningfulChars / charCount;

    const words = text.toLowerCase().split(/\s+/);
    const uniqueWords = new Set(words);
    const repetitionRatio = 1 - (uniqueWords.size / words.length);

    return {
      charCount,
      wordCount,
      meaningfulRatio: Math.round(meaningfulRatio * 100) / 100,
      repetitionRatio: Math.round(repetitionRatio * 100) / 100
    };
  }

  countWords(text) {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  createChunkFromPages(pages) {
    const combinedText = pages.map(p => p.text).join('\n\n');
    const wordCount = this.countWords(combinedText);

    return {
      text: combinedText,
      wordCount: wordCount,
      charCount: combinedText.length,
      pageRange: {
        start: pages[0].pageNumber || pages[0].index + 1,
        end: pages[pages.length - 1].pageNumber || pages[pages.length - 1].index + 1
      },
      pageCount: pages.length,
      qualityScore: this.calculateQualityScore(combinedText),
      sourcePages: pages.map(p => ({
        pageNumber: p.pageNumber,
        originalIndex: p.index,
        wordCount: p.wordCount
      }))
    };
  }

  calculateOverlapPages(pages) {
    const targetOverlapWords = Math.max(
      this.config.minOverlapWords,
      Math.round(pages.reduce((sum, p) => sum + p.wordCount, 0) * this.config.overlapPercent)
    );

    const overlapPages = [];
    let overlapWords = 0;

    for (let i = pages.length - 1; i >= 0 && overlapWords < targetOverlapWords; i--) {
      overlapPages.unshift(pages[i]);
      overlapWords += pages[i].wordCount;
    }

    return overlapPages;
  }

  applyFinalOptimization(chunks) {
    // Merge very small chunks with adjacent ones
    const optimized = [];

    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];

      if (chunk.wordCount < this.config.minChunkSize && i < chunks.length - 1) {
        // Try to merge with next chunk
        const nextChunk = chunks[i + 1];
        if (chunk.wordCount + nextChunk.wordCount <= this.config.maxChunkSize) {
          const mergedChunk = this.mergeChunks(chunk, nextChunk);
          optimized.push(mergedChunk);
          i++; // Skip next chunk as it's been merged
          continue;
        }
      }

      optimized.push(chunk);
    }

    return optimized;
  }

  mergeChunks(chunk1, chunk2) {
    return {
      text: chunk1.text + '\n\n' + chunk2.text,
      wordCount: chunk1.wordCount + chunk2.wordCount,
      charCount: chunk1.charCount + chunk2.charCount + 2,
      pageRange: {
        start: chunk1.pageRange.start,
        end: chunk2.pageRange.end
      },
      pageCount: chunk1.pageCount + chunk2.pageCount,
      qualityScore: this.calculateQualityScore(chunk1.text + '\n\n' + chunk2.text),
      sourcePages: [...chunk1.sourcePages, ...chunk2.sourcePages],
      merged: true
    };
  }

  createOptimizedMetadata(chunks, documentInfo) {
    return chunks.map((chunk, index) => ({
      chunkIndex: index,
      chunkType: 'semantic',
      pageRange: chunk.pageRange,
      pageCount: chunk.pageCount,
      wordCount: chunk.wordCount,
      charCount: chunk.charCount,
      qualityScore: chunk.qualityScore,
      sourcePages: chunk.sourcePages,
      merged: chunk.merged || false,
      // Document-level metadata stored separately to reduce duplication
      documentRef: {
        filename: documentInfo.filename,
        documentId: documentInfo.uploadId,
        totalPages: documentInfo.totalPages
      }
    }));
  }

  generateChunkingReport(originalCount, finalChunks, processingTime, documentInfo) {
    const totalWords = finalChunks.reduce((sum, chunk) => sum + chunk.wordCount, 0);
    const avgChunkSize = Math.round(totalWords / finalChunks.length);
    const reductionPercent = Math.round((1 - finalChunks.length / originalCount) * 100);

    return {
      original: {
        pageCount: originalCount,
        estimatedApiCalls: originalCount
      },
      optimized: {
        chunkCount: finalChunks.length,
        apiCalls: finalChunks.length,
        avgChunkSize: avgChunkSize,
        totalWords: totalWords
      },
      improvement: {
        reductionPercent: reductionPercent,
        apiCallReduction: originalCount - finalChunks.length,
        costSavings: `${reductionPercent}%`,
        processingTime: processingTime
      },
      quality: {
        avgQualityScore: finalChunks.reduce((sum, chunk) => sum + chunk.qualityScore.meaningfulRatio, 0) / finalChunks.length,
        chunksWithGoodQuality: finalChunks.filter(chunk => chunk.qualityScore.meaningfulRatio > 0.7).length
      }
    };
  }
}

module.exports = new SmartChunkingService();
