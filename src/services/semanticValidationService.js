const vectorSearchService = require('./vectorSearchService');
const qdrantService = require('./qdrantService');
const outOfDomainConfig = require('../config/outOfDomainConfig');

/**
 * Semantic Validation Service
 * Implements semantic-first validation flow for enhanced guardrails
 */
class SemanticValidationService {
    constructor() {
        this.relevanceThreshold = parseFloat(process.env.SEMANTIC_RELEVANCE_THRESHOLD) || 0.3;
        this.highConfidenceThreshold = parseFloat(process.env.SEMANTIC_HIGH_CONFIDENCE_THRESHOLD) || 0.7;
    }

    /**
     * Perform semantic relevance check before guardrails
     * @param {string} query - User query
     * @param {string} appId - Application ID
     * @param {Array} documents - Available documents
     * @returns {Promise<Object>} Semantic validation result
     */
    async checkSemanticRelevance(query, appId, documents = []) {
        try {
            console.log(`🔍 SEMANTIC PRE-CHECK: Starting relevance analysis`);
            console.log(`   Query: "${query}"`);
            console.log(`   AppId: ${appId}`);
            console.log(`   Documents available: ${documents.length}`);

            // Step 1: Quick vector similarity check
            const startTime = Date.now();
            const similarityResult = await this.performQuickSimilarityCheck(query, appId);
            const duration = Date.now() - startTime;

            console.log(`⏱️ Semantic check completed in ${duration}ms`);
            console.log(`📊 Similarity score: ${similarityResult.maxScore.toFixed(3)}`);
            console.log(`📄 Relevant chunks found: ${similarityResult.relevantChunks.length}`);

            // Step 2: Enhanced out-of-domain detection
            const outOfDomainResult = this.detectOutOfDomain(query, similarityResult, documents);
            console.log(`🚫 Out-of-domain check: ${outOfDomainResult.isOutOfDomain ? 'YES' : 'NO'} (confidence: ${outOfDomainResult.confidence.toFixed(3)})`);
            if (outOfDomainResult.reasoning) {
                console.log(`   Reasoning: ${outOfDomainResult.reasoning}`);
            }

            // Step 3: Determine relevance level with out-of-domain consideration
            const relevanceLevel = this.determineRelevanceLevel(similarityResult.maxScore, outOfDomainResult);

            // Step 4: Build context for guardrails
            const context = this.buildGuardrailsContext(query, similarityResult, documents, appId, outOfDomainResult);

            const result = {
                isRelevant: similarityResult.maxScore >= this.relevanceThreshold && !outOfDomainResult.isOutOfDomain,
                relevanceLevel,
                semanticScore: similarityResult.maxScore,
                relevantChunks: similarityResult.relevantChunks,
                guardrailsContext: context,
                processingTime: duration,
                outOfDomain: outOfDomainResult
            };

            console.log(`🎯 SEMANTIC PRE-CHECK RESULT:`);
            console.log(`   Relevant: ${result.isRelevant}`);
            console.log(`   Level: ${result.relevanceLevel}`);
            console.log(`   Score: ${result.semanticScore.toFixed(3)}`);
            console.log(`   Out-of-domain: ${result.outOfDomain.isOutOfDomain}`);

            return result;

        } catch (error) {
            console.error('❌ Semantic validation error:', error.message);
            // Fallback: assume relevant to avoid blocking legitimate queries
            return {
                isRelevant: true,
                relevanceLevel: 'unknown',
                semanticScore: 0.5,
                relevantChunks: [],
                guardrailsContext: this.buildFallbackContext(query, appId),
                processingTime: 0,
                outOfDomain: { isOutOfDomain: false, confidence: 0, reasoning: 'Error fallback' },
                error: error.message
            };
        }
    }

    /**
     * Perform quick vector similarity check
     * @param {string} query - User query
     * @param {string} appId - Application ID
     * @returns {Promise<Object>} Similarity results
     */
    async performQuickSimilarityCheck(query, appId) {
        try {
            // Initialize Qdrant if needed
            await qdrantService.initialize();

            // Perform vector search with lower limit for speed
            const searchLimit = 5; // Reduced for quick check
            const baseThreshold = 0.1; // Lower threshold for broader search

            const chunks = await qdrantService.searchSimilarChunks(query, appId, searchLimit, baseThreshold);

            // Calculate max score and filter relevant chunks
            const maxScore = chunks.length > 0 ? Math.max(...chunks.map(chunk => chunk.score)) : 0;
            const relevantChunks = chunks.filter(chunk => chunk.score >= this.relevanceThreshold);

            return {
                maxScore,
                relevantChunks,
                totalChunks: chunks.length
            };

        } catch (error) {
            console.error('❌ Quick similarity check failed:', error.message);
            return {
                maxScore: 0,
                relevantChunks: [],
                totalChunks: 0
            };
        }
    }

    /**
     * Determine relevance level based on semantic score and out-of-domain detection
     * @param {number} score - Semantic similarity score
     * @param {Object} outOfDomainResult - Out-of-domain detection result
     * @returns {string} Relevance level
     */
    determineRelevanceLevel(score, outOfDomainResult = null) {
        // If clearly out-of-domain, always return 'out-of-domain'
        if (outOfDomainResult && outOfDomainResult.isOutOfDomain) {
            return 'out-of-domain';
        }

        // Enhanced thresholds for better classification
        if (score >= this.highConfidenceThreshold) {
            return 'high'; // Very relevant to knowledge base
        } else if (score >= 0.5) {
            return 'medium'; // Somewhat relevant
        } else if (score >= this.relevanceThreshold) {
            return 'low'; // Marginally relevant
        } else {
            return 'irrelevant'; // Not relevant at all
        }
    }

    /**
     * Enhanced out-of-domain detection with multiple strategies
     * @param {string} query - User query
     * @param {Object} similarityResult - Similarity check results
     * @param {Array} documents - Available documents
     * @returns {Object} Out-of-domain analysis
     */
    detectOutOfDomain(query, similarityResult, documents) {
        const queryLower = query.toLowerCase();

        // Strategy 1: Domain-specific keyword detection
        const domainAnalysis = this.analyzeDomainKeywords(queryLower);

        // Strategy 2: Pattern-based out-of-domain detection
        const patternAnalysis = this.analyzeOutOfDomainPatterns(query);

        // Strategy 3: Semantic score analysis
        const semanticAnalysis = this.analyzeSemanticScore(similarityResult.maxScore);

        // Strategy 4: Query intent classification
        const intentAnalysis = this.classifyQueryIntent(queryLower);

        // Combine all strategies for final decision
        const combinedConfidence = this.combineOutOfDomainSignals(
            domainAnalysis,
            patternAnalysis,
            semanticAnalysis,
            intentAnalysis
        );

        const isOutOfDomain = combinedConfidence.score > 0.6;

        return {
            isOutOfDomain,
            confidence: combinedConfidence.score,
            hasDomainKeywords: domainAnalysis.hasDomainKeywords,
            matchedPatterns: patternAnalysis.matchedPatterns,
            queryIntent: intentAnalysis.intent,
            reasoning: this.buildEnhancedOutOfDomainReasoning(
                query,
                similarityResult,
                domainAnalysis,
                patternAnalysis,
                semanticAnalysis,
                intentAnalysis,
                combinedConfidence
            ),
            strategies: {
                domain: domainAnalysis,
                patterns: patternAnalysis,
                semantic: semanticAnalysis,
                intent: intentAnalysis
            }
        };
    }

    /**
     * Analyze domain-specific keywords
     */
    analyzeDomainKeywords(queryLower) {
        // Use configurable domain keywords
        const hrPolicyKeywords = outOfDomainConfig.domainKeywords.hr_policies;
        const generalKeywords = outOfDomainConfig.domainKeywords.general;
        const allKeywords = [...hrPolicyKeywords, ...generalKeywords];

        const foundKeywords = allKeywords.filter(keyword =>
            queryLower.includes(keyword)
        );

        return {
            hasDomainKeywords: foundKeywords.length > 0,
            foundKeywords,
            keywordCount: foundKeywords.length,
            confidence: foundKeywords.length > 0 ? outOfDomainConfig.thresholds.domainKeywordConfidence : 0.2
        };
    }

    /**
     * Analyze out-of-domain patterns
     */
    analyzeOutOfDomainPatterns(query) {
        // Use configurable out-of-domain patterns
        const outOfDomainPatterns = outOfDomainConfig.outOfDomainPatterns;

        const matchedPatterns = outOfDomainPatterns.filter(item =>
            item.pattern.test(query)
        );

        // Calculate weighted confidence based on matched patterns
        const totalConfidence = matchedPatterns.reduce((sum, item) => sum + item.confidence, 0);
        const avgConfidence = matchedPatterns.length > 0 ? totalConfidence / matchedPatterns.length : 0.1;

        return {
            matchedPatterns: matchedPatterns.map(item => item.category),
            patternDetails: matchedPatterns,
            patternCount: matchedPatterns.length,
            confidence: Math.min(avgConfidence, 1.0),
            isOutOfDomain: matchedPatterns.length > 0
        };
    }

    /**
     * Analyze semantic score for out-of-domain indicators
     */
    analyzeSemanticScore(score) {
        let confidence = 0;
        let reasoning = '';

        // Use configurable thresholds
        const veryLowThreshold = outOfDomainConfig.thresholds.semanticVeryLowThreshold;
        const lowThreshold = outOfDomainConfig.thresholds.semanticLowThreshold;

        if (score < veryLowThreshold) {
            confidence = 0.9;
            reasoning = 'Very low semantic similarity indicates out-of-domain';
        } else if (score < lowThreshold) {
            confidence = 0.7;
            reasoning = 'Low semantic similarity suggests out-of-domain';
        } else if (score < 0.6) {
            confidence = 0.4;
            reasoning = 'Moderate semantic similarity, unclear domain relevance';
        } else {
            confidence = 0.1;
            reasoning = 'High semantic similarity suggests in-domain';
        }

        return {
            score,
            confidence,
            reasoning,
            isLowSimilarity: score < lowThreshold
        };
    }

    /**
     * Classify query intent
     */
    classifyQueryIntent(queryLower) {
        const intentPatterns = outOfDomainConfig.intentPatterns;

        for (const [intent, config] of Object.entries(intentPatterns)) {
            if (config.pattern.test(queryLower)) {
                return {
                    intent,
                    confidence: config.confidence,
                    isOutOfScope: config.isOutOfScope
                };
            }
        }

        return {
            intent: 'unknown',
            confidence: 0.5,
            isOutOfScope: false
        };
    }

    /**
     * Combine all out-of-domain signals using configurable weights
     */
    combineOutOfDomainSignals(domainAnalysis, patternAnalysis, semanticAnalysis, intentAnalysis) {
        let totalScore = 0;
        let weights = [];
        const config = outOfDomainConfig.weights;

        // Domain keywords (negative indicator for out-of-domain)
        const domainWeight = domainAnalysis.hasDomainKeywords ? -config.domainKeywords : config.domainKeywords * 0.5;
        totalScore += domainWeight;
        weights.push(`Domain keywords (${domainWeight.toFixed(2)}): ${domainAnalysis.hasDomainKeywords ? 'found' : 'not found'}`);

        // Pattern matching (positive indicator for out-of-domain)
        if (patternAnalysis.isOutOfDomain) {
            const patternWeight = config.patterns * patternAnalysis.confidence;
            totalScore += patternWeight;
            weights.push(`Patterns (+${patternWeight.toFixed(2)}): ${patternAnalysis.matchedPatterns.join(', ')}`);
        }

        // Semantic score analysis
        const semanticWeight = semanticAnalysis.confidence * config.semantic;
        totalScore += semanticWeight;
        weights.push(`Semantic (+${semanticWeight.toFixed(2)}): ${semanticAnalysis.reasoning}`);

        // Intent analysis
        if (intentAnalysis.isOutOfScope) {
            const intentWeight = config.intent * intentAnalysis.confidence;
            totalScore += intentWeight;
            weights.push(`Intent (+${intentWeight.toFixed(2)}): ${intentAnalysis.intent}`);
        }

        // Normalize score to 0-1 range
        const normalizedScore = Math.max(0, Math.min(1, totalScore));

        return {
            score: normalizedScore,
            reasoning: weights.join(' | '),
            components: {
                domain: domainWeight,
                patterns: patternAnalysis.isOutOfDomain ? config.patterns * patternAnalysis.confidence : 0,
                semantic: semanticWeight,
                intent: intentAnalysis.isOutOfScope ? config.intent * intentAnalysis.confidence : 0
            },
            threshold: outOfDomainConfig.thresholds.outOfDomainConfidence
        };
    }

    /**
     * Build enhanced reasoning for out-of-domain detection
     */
    buildEnhancedOutOfDomainReasoning(query, similarityResult, domainAnalysis, patternAnalysis, semanticAnalysis, intentAnalysis, combinedConfidence) {
        const reasons = [];

        // Domain keyword analysis
        if (domainAnalysis.hasDomainKeywords) {
            reasons.push(`Domain keywords found: ${domainAnalysis.foundKeywords.join(', ')}`);
        } else {
            reasons.push('No domain-specific keywords detected');
        }

        // Pattern analysis
        if (patternAnalysis.isOutOfDomain) {
            reasons.push(`Out-of-domain patterns detected: ${patternAnalysis.matchedPatterns.join(', ')}`);
        }

        // Semantic analysis
        reasons.push(`Semantic similarity: ${similarityResult.maxScore.toFixed(3)} - ${semanticAnalysis.reasoning}`);

        // Intent analysis
        if (intentAnalysis.intent !== 'unknown') {
            reasons.push(`Query intent: ${intentAnalysis.intent}`);
        }

        // Final confidence
        reasons.push(`Combined confidence: ${combinedConfidence.score.toFixed(3)}`);

        return reasons.join(' | ');
    }

    /**
     * Build reasoning for out-of-domain detection (legacy method for backward compatibility)
     */
    buildOutOfDomainReasoning(query, similarityResult, hasDomainKeywords, isOutOfDomain) {
        const reasons = [];

        if (isOutOfDomain) {
            reasons.push('Query matches out-of-domain patterns');
        }

        if (!hasDomainKeywords) {
            reasons.push('No HR policy keywords found');
        }

        if (similarityResult.maxScore < 0.5) {
            reasons.push(`Low semantic similarity (${similarityResult.maxScore.toFixed(3)})`);
        }

        return reasons.join('; ');
    }

    /**
     * Build context for enhanced guardrails validation
     * @param {string} query - User query
     * @param {Object} similarityResult - Similarity check results
     * @param {Array} documents - Available documents
     * @param {string} appId - Application ID
     * @param {Object} outOfDomainResult - Out-of-domain detection result
     * @returns {Object} Guardrails context
     */
    buildGuardrailsContext(query, similarityResult, documents, appId, outOfDomainResult = null) {
        // Determine AI domain based on documents and query
        const aiDomain = this.inferAIDomain(documents, query);
        const aiRole = this.inferAIRole(aiDomain);

        // Build document summaries for context
        const documentSummaries = documents.map(doc => ({
            filename: doc.filename,
            id: doc.id,
            type: doc.type || 'document'
        }));

        // Extract top relevant chunks for context
        const topChunks = similarityResult.relevantChunks
            .slice(0, 3) // Top 3 most relevant
            .map(chunk => ({
                text: chunk.text.substring(0, 200) + '...', // Truncate for context
                score: chunk.score,
                filename: chunk.filename
            }));

        // Enhanced context with out-of-domain information
        const context = {
            semantic_score: similarityResult.maxScore,
            ai_domain: aiDomain,
            ai_role: aiRole,
            has_relevant_content: similarityResult.relevantChunks.length > 0,
            retrieved_documents: topChunks,
            document_summaries: documentSummaries,
            app_id: appId
        };

        // Add out-of-domain information if available
        if (outOfDomainResult) {
            context.out_of_domain = {
                is_out_of_domain: outOfDomainResult.isOutOfDomain,
                confidence: outOfDomainResult.confidence,
                query_intent: outOfDomainResult.queryIntent,
                has_domain_keywords: outOfDomainResult.hasDomainKeywords,
                matched_patterns: outOfDomainResult.matchedPatterns,
                reasoning: outOfDomainResult.reasoning
            };
        }

        return context;
    }

    /**
     * Infer AI domain from available documents using dynamic detection
     * @param {Array} documents - Available documents
     * @param {string} query - User query for additional context
     * @returns {string} Inferred domain
     */
    inferAIDomain(documents, query = '') {
        // DYNAMIC DOMAIN DETECTION: Use the document type detector
        const documentTypeDetector = require('./documentTypeDetector');
        const detectionResult = documentTypeDetector.detectDocumentType(documents, query);

        console.log(`🔍 Dynamic domain detection result:`, {
            domain: detectionResult.domain,
            confidence: detectionResult.confidence,
            detectedKeywords: detectionResult.detectedKeywords.slice(0, 5) // Show first 5 keywords
        });

        return detectionResult.domain;
    }

    /**
     * Infer AI role from domain
     * @param {string} domain - AI domain
     * @returns {string} AI role
     */
    inferAIRole(domain) {
        const roleMap = {
            'hr_policies': 'policy_assistant',
            'project_management': 'project_management_assistant',
            'environmental': 'environmental_policy_assistant',
            'finance': 'financial_policy_assistant',
            'it_security': 'it_security_assistant',
            'documentation': 'documentation_assistant',
            'technical': 'technical_assistant',
            'general': 'document_assistant'
        };

        return roleMap[domain] || 'document_assistant';
    }

    /**
     * Build fallback context when semantic check fails
     * @param {string} query - User query
     * @param {string} appId - Application ID
     * @returns {Object} Fallback context
     */
    buildFallbackContext(query, appId) {
        return {
            semantic_score: 0.5, // Neutral score
            ai_domain: 'general',
            ai_role: 'general_assistant',
            has_relevant_content: false,
            retrieved_documents: [],
            document_summaries: [],
            app_id: appId,
            fallback: true
        };
    }

    /**
     * Check if query should bypass guardrails based on high semantic confidence
     * @param {Object} semanticResult - Semantic validation result
     * @param {string} query - User query
     * @returns {boolean} Whether to bypass guardrails
     */
    shouldBypassGuardrails(semanticResult, query) {
        // High confidence + domain-appropriate query = bypass guardrails
        if (semanticResult.relevanceLevel === 'high' && semanticResult.semanticScore >= this.highConfidenceThreshold) {
            const queryLower = query.toLowerCase();
            const domainKeywords = ['policy', 'policies', 'explain', 'what', 'how', 'tell me', 'show me'];

            if (domainKeywords.some(keyword => queryLower.includes(keyword))) {
                console.log(`✅ BYPASS GUARDRAILS: High confidence domain query`);
                return true;
            }
        }

        return false;
    }
}

module.exports = new SemanticValidationService();
