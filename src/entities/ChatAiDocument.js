// ChatAI Document Entity for direct database access
const { EntitySchema } = require('typeorm');

const ChatAiDocument = new EntitySchema({
  name: 'ChatAiDocument',
  tableName: 'chat_ai_documents',
  columns: {
    id: {
      type: 'int',
      primary: true,
      generated: true,
    },
    filename: {
      type: 'varchar',
      nullable: false,
    },
    filesize: {
      type: 'int',
      nullable: false,
    },
    contentType: {
      type: 'varchar',
      nullable: false,
    },
    status: {
      type: 'varchar',
      nullable: false,
      default: 'uploading',
      comment: 'uploading, parsing, processing, embedding, indexing, ready, error',
    },
    parsedData: {
      type: 'jsonb',
      nullable: true,
      comment: 'Raw parsed data from LlamaIndex',
    },
    summary: {
      type: 'jsonb',
      nullable: true,
      comment: 'AI-generated summary and key insights',
    },
    pageCount: {
      type: 'int',
      nullable: true,
    },
    wordCount: {
      type: 'int',
      nullable: true,
    },
    errorMessage: {
      type: 'text',
      nullable: true,
    },
    indexId: {
      type: 'text',
      nullable: true,
      comment: 'LlamaIndex vector index ID',
    },
    userId: {
      type: 'varchar',
      nullable: false,
      comment: 'Supabase user ID',
    },
    projectId: {
      type: 'varchar',
      nullable: false,
      comment: 'References ChatAi project ID',
    },
    createdAt: {
      type: 'timestamp',
      default: () => 'CURRENT_TIMESTAMP',
      createDate: true,
    },
  },
  relations: {
    project: {
      type: 'many-to-one',
      target: 'ChatAi',
      joinColumn: { name: 'projectId' },
      onDelete: 'CASCADE',
    },
    messages: {
      type: 'one-to-many',
      target: 'ChatAiMessage',
      inverseSide: 'document',
    },
  },
  indices: [
    {
      name: 'IDX_DOCUMENT_PROJECT_STATUS',
      columns: ['projectId', 'status'],
    },
    {
      name: 'IDX_DOCUMENT_USER_STATUS',
      columns: ['userId', 'status'],
    },
    {
      name: 'IDX_DOCUMENT_CREATED_AT',
      columns: ['createdAt'],
    },
  ],
});

module.exports = ChatAiDocument;
