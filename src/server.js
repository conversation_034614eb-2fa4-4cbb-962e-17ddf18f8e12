const express = require('express');
const cors = require('cors');
const path = require('path');

// Import configuration managers
const configManager = require('./config');
const { databaseConfigManager } = require('./config/database');
const { logAwsConfigStatus } = require('./config/awsConfig');
const databaseService = require('./services/databaseService');

const app = express();

// Middleware
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Origin', 'X-Requested-With', 'Accept'],
  credentials: true
}));

app.use(express.json({ limit: '25mb' }));
app.use(express.urlencoded({ extended: true, limit: '25mb' }));

// Serve static files from public directory (only streaming-ui.html)
app.use(express.static(path.join(__dirname, '../public')));

// SDK routes - serve widget at professional URLs
app.get('/sdk/widget.js', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/chat-widget.js'));
});

app.get('/sdk/embed.js', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/chat-widget.js'));
});

// Legacy support - redirect old URL to new SDK URL
app.get('/chat-widget.js', (req, res) => {
  res.redirect(301, '/sdk/widget.js');
});

// Routes
const routes = require('./routes');
app.use('/', routes);

// Error handling middleware
app.use((err, req, res, next) => {
  // console.error('❌ Unhandled error:', err);
  res.status(500).json({
    error: true,
    message: 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: true,
    message: 'Endpoint not found'
  });
});

/**
 * Update all service configurations after main config is loaded
 */
async function updateServiceConfigurations(config) {
  try {
    console.log('🔄 Updating service configurations...');

    // Import services that need configuration updates
    const cacheService = require('./services/hybridCacheService');
    const embeddingService = require('./services/embeddingService');
    const userService = require('./services/userService');
    const openRouterService = require('./services/openRouterService');
    const llamaIndexService = require('./services/llamaIndexService');

    // Update each service configuration
    await Promise.all([
      cacheService.updateConfiguration(config),
      embeddingService.updateConfiguration(config),
      userService.updateConfiguration(config),
      openRouterService.updateConfiguration(config),
      llamaIndexService.updateConfiguration(config)
    ]);

    console.log('✅ All service configurations updated');
  } catch (error) {
    console.warn('⚠️ Some service configurations may not have updated:', error.message);
  }
}

// Initialize server with AWS Secrets Manager, database connection
async function startServer() {
  try {
    console.log('🚀 Starting ChatAI SDK Clean...\n');

    // 1. Log AWS configuration status
    console.log('🔧 Checking AWS Configuration...');
    const awsValidation = logAwsConfigStatus();

    // 2. Initialize configuration with AWS Secrets Manager
    console.log('\n🔧 Initializing configuration...');
    const config = await configManager.initialize();

    // 3. Update all service configurations now that config is ready
    await updateServiceConfigurations(config);

    // 4. Initialize database configuration
    console.log('\n🔧 Initializing database configuration...');
    const databaseConfig = await databaseConfigManager.initialize();

    // 5. Initialize database connection
    console.log('\n🔌 Initializing database connection...');
    await databaseService.initialize();

    // 6. Start Express server
    const PORT = config.port || 3002;

    app.listen(PORT, () => {
      console.log('\n🎉 SERVER STARTED SUCCESSFULLY!');
      console.log('━'.repeat(60));
      console.log(`🌐 Server running on port ${PORT}`);
      console.log(`📊 Environment: ${config.nodeEnv}`);
      console.log(`🔐 AWS Secrets Manager: ${config.aws.isConfigured ? 'Enabled' : 'Disabled'}`);

      console.log('\n📋 Available endpoints:');
      console.log(`   GET  /api/v1/chat/?apikey=...&query=...  - Main chat endpoint`);
      console.log(`   POST /api/vector/upload-and-process - Document upload`);
      console.log(`   GET  /health                        - Health check`);
      console.log(`   GET  /health/aws                    - AWS services health`);

      
    });

  } catch (error) {
    // console.error('\n❌ Failed to start server:', error.message);
    // console.error('💡 Check your AWS configuration and secrets setup');
    process.exit(1);
  }
}

// Graceful shutdown
async function gracefulShutdown(signal) {
  console.log(`\n🛑 Received ${signal}, shutting down gracefully...`);

  try {
    // Close database connection
    await databaseService.close();
    console.log('✅ Database connection closed');

    // You can add other cleanup tasks here
    console.log('✅ Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during shutdown:', error.message);
    process.exit(1);
  }
}

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the server
startServer();

module.exports = app;
