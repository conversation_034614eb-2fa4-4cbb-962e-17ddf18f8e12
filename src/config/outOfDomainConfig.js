/**
 * Configuration for out-of-domain detection
 * Allows fine-tuning of thresholds and patterns
 */

const outOfDomainConfig = {
    // Detection thresholds
    thresholds: {
        // Overall out-of-domain confidence threshold
        outOfDomainConfidence: parseFloat(process.env.OUT_OF_DOMAIN_THRESHOLD) || 0.6,

        // Semantic similarity thresholds
        semanticLowThreshold: parseFloat(process.env.SEMANTIC_LOW_THRESHOLD) || 0.3,
        semanticVeryLowThreshold: parseFloat(process.env.SEMANTIC_VERY_LOW_THRESHOLD) || 0.2,

        // Pattern matching confidence
        patternMatchConfidence: parseFloat(process.env.PATTERN_MATCH_CONFIDENCE) || 0.8,

        // Domain keyword confidence
        domainKeywordConfidence: parseFloat(process.env.DOMAIN_KEYWORD_CONFIDENCE) || 0.8,

        // Misunderstood query threshold
        misunderstoodThreshold: parseFloat(process.env.MISUNDERSTOOD_THRESHOLD) || 0.8
    },

    // Weights for combining different signals
    weights: {
        domainKeywords: parseFloat(process.env.WEIGHT_DOMAIN_KEYWORDS) || 0.3,
        patterns: parseFloat(process.env.WEIGHT_PATTERNS) || 0.6,
        semantic: parseFloat(process.env.WEIGHT_SEMANTIC) || 0.4,
        intent: parseFloat(process.env.WEIGHT_INTENT) || 0.5
    },

    // Domain-specific keywords (can be customized per application)
    domainKeywords: {
        hr_policies: [
            'policy', 'policies', 'travel', 'expense', 'allowance', 'reimbursement',
            'hotel', 'lodging', 'per diem', 'daily allowance', 'transportation',
            'taxi', 'uber', 'metro', 'flight', 'booking', 'approval', 'hod',
            'department head', 'ceo', 'business travel', 'domestic', 'international',
            'mobile', 'phone', 'skype', 'receipt', 'bill', 'claim', 'parking',
            'toll', 'europe', 'asia pacific', 'australia', 'us', 'alcohol',
            'employee', 'staff', 'work', 'office', 'company', 'organization',
            'workplace', 'business', 'corporate', 'professional', 'job'
        ],
        // Can add other domains here
        general: [
            'help', 'question', 'information', 'guide', 'procedure', 'process'
        ]
    },

    // Out-of-domain patterns with categories
    outOfDomainPatterns: [
        {
            // FIXED: More specific political patterns to avoid false positives with "PM" (Project Manager)
            pattern: /\b(prime minister|president|government|politics|election|minister|parliament|congress|political party|vote|voting|democracy|republican|democrat|senator|governor)\b/i,
            category: 'politics',
            confidence: 0.9
        },
        {
            pattern: /\b(weather|temperature|rain|sunny|cloudy|forecast|climate|storm|hurricane|snow|wind|humidity)\b/i,
            category: 'weather',
            confidence: 0.9
        },
        {
            pattern: /\b(programming|coding|software development|app development|website|database|server|api|javascript|python|java|c\+\+|html|css|react|node\.js)\b/i,
            category: 'technology',
            confidence: 0.8
        },
        {
            pattern: /\b(movie|film|song|music|game|sports|football|cricket|entertainment|celebrity|actor|actress|tv show|series|netflix)\b/i,
            category: 'entertainment',
            confidence: 0.9
        },
        {
            pattern: /\b(age|birthday|personal details|family|home address|phone number|email address|social security|private|personal life)\b/i,
            category: 'personal',
            confidence: 0.9
        },
        {
            pattern: /\b(capital of|country|history|science|math|physics|chemistry|biology|geography|who invented|when was|what year)\b/i,
            category: 'general_knowledge',
            confidence: 0.8
        },
        {
            pattern: /\b(symptoms|disease|medicine|doctor|hospital|treatment|diagnosis|cure|health|medical|illness|pain|fever)\b/i,
            category: 'medical',
            confidence: 0.8
        },
        {
            pattern: /\b(buy|purchase|shopping|price|cost|discount|sale|amazon|ebay|store|retail|product|brand)\b/i,
            category: 'shopping',
            confidence: 0.7
        },
        {
            pattern: /\b(recipe|cooking|food|restaurant|menu|cuisine|ingredients|chef|kitchen)\b/i,
            category: 'food',
            confidence: 0.8
        },
        {
            pattern: /\b(stock market|investment|trading|cryptocurrency|bitcoin|finance|banking|loan|mortgage|credit card)\b/i,
            category: 'finance',
            confidence: 0.8
        }
    ],

    // Intent patterns for query classification
    intentPatterns: {
        policy_question: {
            pattern: /\b(what is|explain|tell me about|how does|policy|rule|guideline|procedure)\b/i,
            confidence: 0.8,
            isOutOfScope: false
        },
        factual_lookup: {
            pattern: /\b(who is|what is|when is|where is|how much|how many|which)\b/i,
            confidence: 0.7,
            isOutOfScope: false // Could be either, depends on context
        },
        procedural: {
            pattern: /\b(how to|how do i|steps|process|procedure|guide|instructions)\b/i,
            confidence: 0.8,
            isOutOfScope: false
        },
        general_chat: {
            pattern: /\b(hello|hi|how are you|good morning|thanks|thank you|bye|goodbye)\b/i,
            confidence: 0.9,
            isOutOfScope: false
        },
        out_of_scope: {
            // FIXED: More specific out-of-scope patterns to avoid false positives
            pattern: /\b(weather|news|sports|entertainment|personal|family|celebrity|gossip|fashion|cooking|recipes)\b/i,
            confidence: 0.9,
            isOutOfScope: true
        }
    },

    // Response templates for different categories
    responseTemplates: {
        politics: {
            message: "I can't provide information about political topics.",
            suggestions: [
                "Ask about company policies and workplace guidelines",
                "Try questions like 'What is the travel policy?' or 'How do I submit expenses?'"
            ]
        },
        weather: {
            message: "I don't have access to weather information.",
            suggestions: [
                "I can help with travel policies that might be affected by weather",
                "Ask about business travel guidelines or emergency procedures"
            ]
        },
        entertainment: {
            message: "I'm not able to discuss entertainment topics.",
            suggestions: [
                "I can help with HR policies and workplace questions",
                "Try asking about employee benefits or company events policies"
            ]
        },
        general_knowledge: {
            message: "I'm specialized in HR policies and workplace guidance.",
            suggestions: [
                "Ask about company policies, procedures, or guidelines",
                "I can help with travel, expenses, or workplace-related questions"
            ]
        },
        technology: {
            message: "I focus on HR policies rather than technical topics.",
            suggestions: [
                "I can help with workplace technology policies if relevant",
                "Ask about IT equipment policies or remote work guidelines"
            ]
        },
        medical: {
            message: "I can't provide medical advice.",
            suggestions: [
                "I can help with workplace health and safety policies",
                "Ask about sick leave policies or workplace wellness programs"
            ]
        },
        shopping: {
            message: "I don't handle shopping or purchasing questions.",
            suggestions: [
                "I can help with company procurement policies if relevant",
                "Ask about expense policies or business purchase guidelines"
            ]
        },
        personal: {
            message: "I can't help with personal information requests.",
            suggestions: [
                "I can assist with workplace policies and procedures",
                "Try asking about HR policies or employee guidelines"
            ]
        },
        food: {
            message: "I don't provide cooking or restaurant information.",
            suggestions: [
                "I can help with meal allowance policies for business travel",
                "Ask about expense policies for business meals"
            ]
        },
        finance: {
            message: "I don't provide financial or investment advice.",
            suggestions: [
                "I can help with expense reimbursement policies",
                "Ask about travel allowances or business expense guidelines"
            ]
        }
    },

    // Fallback response for unclassified out-of-domain queries
    fallbackResponse: {
        message: "Your question doesn't seem to be related to HR policies or workplace topics.",
        suggestions: [
            "Please ask about travel policies, expense guidelines, or workplace procedures",
            "Try questions like 'What are the travel allowances?' or 'How do I claim expenses?'"
        ]
    },

    // Settings for misunderstood query detection
    misunderstoodQuerySettings: {
        enabled: true,
        potentialHRTerms: [
            'company', 'work', 'office', 'employee', 'staff', 'business',
            'travel', 'expense', 'policy', 'guideline', 'procedure', 'workplace'
        ],
        maxAlternatives: 3
    }
};

module.exports = outOfDomainConfig;
