require('dotenv').config();

/**
 * AWS Configuration
 * Centralized AWS settings and environment variables
 */
const awsConfig = {
  // AWS Credentials Configuration
  credentials: {
    // These will be automatically picked up by AWS SDK in this order:
    // 1. Environment variables (AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY)
    // 2. AWS credentials file (~/.aws/credentials)
    // 3. IAM role if running on EC2/ECS/Lambda
    // 4. AWS SSO
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    sessionToken: process.env.AWS_SESSION_TOKEN, // For temporary credentials
    profile: process.env.AWS_PROFILE, // For AWS CLI profiles
  },

  // AWS Secrets Manager Configuration
  secretsManager: {
    region: process.env.AWS_REGION || process.env.AWS_DEFAULT_REGION || 'us-east-1',
    secretName: process.env.AWS_SECRET_NAME || 'chatai-service-secrets',
    cacheTimeout: parseInt(process.env.AWS_SECRETS_CACHE_TIMEOUT) || 5 * 60 * 1000, // 5 minutes
  },

  // AWS IAM Role Configuration (for ECS/EC2 deployments)
  iam: {
    roleArn: process.env.AWS_IAM_ROLE_ARN,
    roleSessionName: process.env.AWS_ROLE_SESSION_NAME || 'chatai-service-session',
  },

  // AWS Web Identity Token (for EKS/IRSA)
  webIdentity: {
    tokenFile: process.env.AWS_WEB_IDENTITY_TOKEN_FILE,
    roleArn: process.env.AWS_ROLE_ARN,
  },

  // Environment Detection
  environment: {
    isAws: !!(process.env.AWS_EXECUTION_ENV || process.env.AWS_LAMBDA_FUNCTION_NAME || process.env.ECS_CONTAINER_METADATA_URI),
    isLambda: !!process.env.AWS_LAMBDA_FUNCTION_NAME,
    isEcs: !!process.env.ECS_CONTAINER_METADATA_URI,
    isEc2: !!process.env.AWS_EXECUTION_ENV,
  },

  // Configuration Validation
  validation: {
    requiredInProduction: process.env.NODE_ENV === 'production',
    warnOnMissing: process.env.AWS_CONFIG_WARN_ON_MISSING !== 'false',
  }
};

/**
 * Validate AWS configuration
 * @returns {Object} Validation result
 */
function validateAwsConfig() {
  const validation = {
    isValid: false,
    hasCredentials: false,
    credentialSource: 'none',
    warnings: [],
    errors: []
  };

  // Check for credentials
  if (awsConfig.credentials.accessKeyId && awsConfig.credentials.secretAccessKey) {
    validation.hasCredentials = true;
    validation.credentialSource = 'environment_variables';
  } else if (awsConfig.credentials.profile) {
    validation.hasCredentials = true;
    validation.credentialSource = 'aws_profile';
  } else if (awsConfig.iam.roleArn) {
    validation.hasCredentials = true;
    validation.credentialSource = 'iam_role';
  } else if (awsConfig.webIdentity.tokenFile && awsConfig.webIdentity.roleArn) {
    validation.hasCredentials = true;
    validation.credentialSource = 'web_identity_token';
  } else if (awsConfig.environment.isAws) {
    validation.hasCredentials = true;
    validation.credentialSource = 'aws_metadata_service';
  }

  // Validate region
  if (!awsConfig.secretsManager.region) {
    validation.errors.push('AWS_REGION is required');
  }

  // Validate secret name
  if (!awsConfig.secretsManager.secretName) {
    validation.warnings.push('AWS_SECRET_NAME not set, using default: chatai-service-secrets');
  }

  // Production validation
  if (awsConfig.validation.requiredInProduction && process.env.NODE_ENV === 'production') {
    if (!validation.hasCredentials) {
      validation.errors.push('AWS credentials are required in production environment');
    }
  } else if (!validation.hasCredentials) {
    validation.warnings.push('AWS credentials not found, will fallback to environment variables for secrets');
  }

  validation.isValid = validation.errors.length === 0;

  return validation;
}

/**
 * Log AWS configuration status
 */
function logAwsConfigStatus() {
  const validation = validateAwsConfig();

  // console.log('\n🔧 AWS CONFIGURATION STATUS');
  // console.log('━'.repeat(50));
  // console.log(`🌍 Region: ${awsConfig.secretsManager.region}`);
  // console.log(`🔐 Secret Name: ${awsConfig.secretsManager.secretName}`);
  // console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  // console.log(`🏢 AWS Environment: ${awsConfig.environment.isAws ? 'Yes' : 'No'}`);

  if (awsConfig.environment.isAws) {
    // console.log(`   🔸 Lambda: ${awsConfig.environment.isLambda ? 'Yes' : 'No'}`);
    // console.log(`   🔸 ECS: ${awsConfig.environment.isEcs ? 'Yes' : 'No'}`);
    // console.log(`   🔸 EC2: ${awsConfig.environment.isEc2 ? 'Yes' : 'No'}`);
  }

  console.log(`🔑 Credentials: ${validation.hasCredentials ? '✅ Available' : '❌ Not Found'}`);
  if (validation.hasCredentials) {
    console.log(`   🔸 Source: ${validation.credentialSource}`);
  }

  if (validation.warnings.length > 0) {
    console.log('\n⚠️ WARNINGS:');
    validation.warnings.forEach(warning => console.log(`   • ${warning}`));
  }

  if (validation.errors.length > 0) {
    console.log('\n❌ ERRORS:');
    validation.errors.forEach(error => console.log(`   • ${error}`));
  }

  console.log('━'.repeat(50));

  return validation;
}

/**
 * Get minimal AWS client configuration
 * @returns {Object} AWS client configuration
 */
function getAwsClientConfig() {
  const config = {
    region: awsConfig.secretsManager.region,
  };

  // Add explicit credentials if provided via environment variables
  if (awsConfig.credentials.accessKeyId && awsConfig.credentials.secretAccessKey) {
    config.credentials = {
      accessKeyId: awsConfig.credentials.accessKeyId,
      secretAccessKey: awsConfig.credentials.secretAccessKey,
    };

    if (awsConfig.credentials.sessionToken) {
      config.credentials.sessionToken = awsConfig.credentials.sessionToken;
    }
  }

  return config;
}

module.exports = {
  awsConfig,
  validateAwsConfig,
  logAwsConfigStatus,
  getAwsClientConfig
}; 