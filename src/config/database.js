// Database configuration for ChatAI-SDK-Clean
require('dotenv').config();
const awsSecretsManager = require('../services/awsSecretsManagerService');

class DatabaseConfigManager {
  constructor() {
    this.databaseConfig = null;
    this.isInitialized = false;
  }

  /**
   * Initialize database configuration with AWS Secrets Manager
   */
  async initialize() {
    if (this.isInitialized) {
      return this.databaseConfig;
    }

    try {
      console.log('🔧 Initializing database configuration with AWS Secrets Manager...');
      
      // Get database credentials from AWS Secrets Manager (with environment variable fallback)
      const dbCredentials = await awsSecretsManager.getDatabaseConfig();
      console.log('dbCredentials', dbCredentials)

      this.databaseConfig = {
        type: 'postgres',
        // host: process.env.POSTGRES_HOST,
        // port: process.env.POSTGRES_PORT,
        // username: process.env.POSTGRES_USER,
        // password: process.env.POSTGRES_PASSWORD,
        // database: process.env.POSTGRES_DB,
        
        host: dbCredentials.host,
        port: dbCredentials.port,
        username: dbCredentials.username,
        password: dbCredentials.password,
        database: dbCredentials.database,
        
        // Connection pool settings
        extra: {
          max: 20, // Maximum number of connections
          min: 5,  // Minimum number of connections
          acquire: 30000, // Maximum time to get connection
          idle: 10000,    // Maximum time connection can be idle
        },
        
        // TypeORM settings
        synchronize: false, // Don't auto-sync schema (User-Service manages this)
        logging: process.env.NODE_ENV === 'development' ? ['query', 'error'] : ['error'],
        logger: 'simple-console',
        
        // Connection options
        options: {
          encrypt: process.env.NODE_ENV === 'production',
          trustServerCertificate: process.env.NODE_ENV !== 'production',
        },
        
        // Retry connection settings
        retryAttempts: 3,
        retryDelay: 3000,
        autoLoadEntities: true,
        
        // Entities will be loaded dynamically
        entities: [],
      };

      // Validate database configuration
      await this.validateDatabaseConfig();

      this.isInitialized = true;
      console.log('✅ Database configuration initialized successfully');
      console.log(`🔐 AWS Secrets Manager: ${awsSecretsManager.isConfigured ? 'Enabled' : 'Disabled (using env vars)'}`);

      return this.databaseConfig;
    } catch (error) {
      console.error('❌ Failed to initialize database configuration:', error.message);
      throw error;
    }
  }

  /**
   * Validate database configuration
   */
  async validateDatabaseConfig() {
    const requiredDbSecrets = ['POSTGRES_HOST', 'POSTGRES_USER', 'POSTGRES_PASSWORD', 'POSTGRES_DB'];
    const validation = await awsSecretsManager.validateSecrets(requiredDbSecrets);
    
    if (!validation.isValid) {
      console.warn('⚠️ Missing database secrets:', validation.missing);
      console.warn('Using default values for development');
    } else {
      console.log('✅ All database secrets are available');
    }
    
    console.log('📊 Database Configuration:');
    console.log(`   Host: ${this.databaseConfig.host}:${this.databaseConfig.port}`);
    console.log(`   Database: ${this.databaseConfig.database}`);
    console.log(`   User: ${this.databaseConfig.username}`);
    console.log(`   Logging: ${this.databaseConfig.logging}`);
    console.log(`   Credentials Source: ${awsSecretsManager.isConfigured ? 'AWS Secrets Manager' : 'Environment Variables'}`);
  }

  /**
   * Get database configuration (ensure it's initialized first)
   */
  getDatabaseConfig() {
    if (!this.isInitialized) {
      throw new Error('Database configuration not initialized. Call initialize() first.');
    }
    return this.databaseConfig;
  }

  /**
   * Refresh database configuration by clearing cache and re-initializing
   */
  async refresh() {
    console.log('🔄 Refreshing database configuration...');
    awsSecretsManager.clearCache();
    this.isInitialized = false;
    this.databaseConfig = null;
    return this.initialize();
  }

  /**
   * Test database connection with current configuration
   */
  async testConnection() {
    if (!this.isInitialized) {
      throw new Error('Database configuration not initialized');
    }

    const { DataSource } = require('typeorm');
    
    try {
      console.log('🔌 Testing database connection...');
      
      const testDataSource = new DataSource({
        ...this.databaseConfig,
        entities: [], // No entities needed for connection test
      });

      await testDataSource.initialize();
      console.log('✅ Database connection test successful');
      await testDataSource.destroy();
      
      return { success: true, message: 'Connection successful' };
    } catch (error) {
      console.error('❌ Database connection test failed:', error.message);
      return { success: false, message: error.message };
    }
  }

  /**
   * Get health status of database configuration
   */
  async getHealthStatus() {
    const awsHealth = await awsSecretsManager.healthCheck();
    
    return {
      databaseConfig: {
        initialized: this.isInitialized,
        host: this.databaseConfig?.host || 'Not initialized',
        database: this.databaseConfig?.database || 'Not initialized',
        timestamp: new Date().toISOString()
      },
      awsSecretsManager: awsHealth
    };
  }
}

// Create singleton instance
const databaseConfigManager = new DatabaseConfigManager();

module.exports = {
  databaseConfigManager,
  // For backwards compatibility, provide the old function
  validateDatabaseConfig: async () => {
    await databaseConfigManager.initialize();
    return databaseConfigManager.validateDatabaseConfig();
  },
  // For backwards compatibility, provide a getter for databaseConfig
  get databaseConfig() {
    return databaseConfigManager.getDatabaseConfig();
  }
};
