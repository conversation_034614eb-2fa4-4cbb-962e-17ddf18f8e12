/**
 * Test the response generation directly
 */

const outOfDomainResponseService = require('./src/services/outOfDomainResponseService');

// Mock out-of-domain result for "PM of America"
const mockOutOfDomainResult = {
    isOutOfDomain: true,
    confidence: 0.97,
    hasDomainKeywords: false,
    matchedPatterns: ['politics'],
    queryIntent: 'factual_lookup',
    reasoning: 'No domain-specific keywords detected | Out-of-domain patterns detected: politics | Semantic similarity: 0.701 - High semantic similarity suggests in-domain | Combined confidence: 0.730'
};

const mockSemanticResult = {
    semanticScore: 0.701
};

// Test different AI contexts
const testContexts = [
    {
        name: 'HR Policies',
        context: { ai_domain: 'hr_policies', ai_role: 'policy_assistant' }
    },
    {
        name: 'Technical Documentation',
        context: { ai_domain: 'technical', ai_role: 'technical_assistant' }
    },
    {
        name: 'Legal Documents',
        context: { ai_domain: 'legal', ai_role: 'legal_assistant' }
    },
    {
        name: 'Medical Information',
        context: { ai_domain: 'medical', ai_role: 'medical_assistant' }
    }
];

console.log('🧪 Testing Domain-Aware Out-of-Domain Response Generation\n');

testContexts.forEach(({ name, context }) => {
    console.log(`\n📋 Testing ${name} Domain:`);
    console.log(`   AI Domain: ${context.ai_domain}`);
    console.log(`   AI Role: ${context.ai_role}`);

    // Test response generation with AI context
    const response = outOfDomainResponseService.generateResponse(
        "PM of America",
        mockOutOfDomainResult,
        mockSemanticResult,
        context
    );

    console.log('📝 Generated Response:');
    console.log('   Message:', response.message);
    console.log('   Suggestions:', response.suggestions);
    console.log('   AI Domain:', response.ai_domain);
    console.log('   AI Role:', response.ai_role);

    // Test validation errors generation
    const validationErrors = outOfDomainResponseService.generateValidationErrors(response);

    console.log('\n🚫 Validation Errors:');
    validationErrors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
    });

});

console.log('\n✅ Domain-aware response generation test complete!');
