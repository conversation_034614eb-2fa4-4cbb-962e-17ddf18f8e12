version: "3"

services:
  qdrant:
    image: qdrant/qdrant:v1.14.1
    container_name: qdrant
    restart: unless-stopped
    ports:
      - "127.0.0.1:6333:6333" # HTTP API - localhost only
      - "127.0.0.1:6334:6334" # gRPC API - localhost only
    volumes:
      - qdrant_storage:/qdrant/storage
    env_file:
      - .env.docker
    environment:
      QDRANT__SERVICE__HTTP_PORT: 6333
      QDRANT__SERVICE__GRPC_PORT: 6334
      # API Key for authentication - loaded from .env.docker file
      QDRANT__SERVICE__API_KEY: ${QDRANT_API_KEY}
      # Optional: Read-only API key for applications that only need read access
      # QDRANT__SERVICE__READ_ONLY_API_KEY: ${QDRANT_READ_ONLY_API_KEY}
    networks:
      - chatai_internal

  redis:
    image: redis:7.4-alpine
    container_name: chatai_redis
    restart: unless-stopped
    ports:
      - "127.0.0.1:6379:6379" # Redis port - localhost only
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    env_file:
      - .env.docker
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      # Redis password from environment
      REDIS_PASSWORD: ${REDIS_PASSWORD:-chatai_redis_2024}
    networks:
      - chatai_internal
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  redis-insight:
    image: redis/redisinsight:latest
    container_name: chatai_redis_insight
    restart: unless-stopped
    ports:
      - "127.0.0.1:5540:5540" # RedisInsight web UI - localhost only
    volumes:
      - redis_insight_data:/data
    networks:
      - chatai_internal
    depends_on:
      - redis

volumes:
  qdrant_storage:
    driver: local
  redis_data:
    driver: local
  redis_insight_data:
    driver: local

networks:
  chatai_internal:
    driver: bridge
    internal: false # Allow external access for Redis
    ipam:
      config:
        - subnet: **********/16
