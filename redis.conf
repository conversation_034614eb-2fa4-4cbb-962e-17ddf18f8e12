# Redis Configuration for ChatAI Service
# Optimized for caching and session management

# Network and Security
bind 0.0.0.0
port 6379
protected-mode yes
requirepass chatai_redis_2024

# Memory Management
maxmemory 512mb
maxmemory-policy allkeys-lru

# Persistence Configuration
# Use RDB for periodic snapshots
save 900 1
save 300 10
save 60 10000

# RDB Configuration
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# AOF Configuration (disabled for performance)
appendonly no

# Logging
loglevel notice
logfile ""

# Client Configuration
timeout 300
tcp-keepalive 300
tcp-backlog 511

# Performance Tuning
databases 16
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# Slow Log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Latency Monitoring
latency-monitor-threshold 100

# Key Expiration
# Enable active expiration of keys
hz 10

# Client Output Buffer Limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Disable dangerous commands in production
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""
rename-command CONFIG ""
rename-command SHUTDOWN SHUTDOWN_CHATAI_2024
