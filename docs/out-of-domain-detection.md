# Enhanced Out-of-Domain Detection System

## Overview

This document describes the comprehensive out-of-domain detection system implemented to handle queries that fall outside the AI assistant's domain expertise (HR policies and workplace guidance).

## Problem Statement

The original system had issues with queries like "Who is the PM of India?" receiving a semantic similarity score of 0.67 (classified as "medium" relevance) when it should be clearly identified as out-of-domain.

## Solution Architecture

### 1. Multi-Strategy Detection Framework

The enhanced system uses four complementary strategies:

#### A. Domain Keyword Analysis
- **Purpose**: Identify presence of domain-specific keywords
- **Keywords**: HR policies, travel, expense, workplace terms
- **Logic**: Presence reduces out-of-domain confidence; absence increases it
- **Weight**: 0.3 (configurable)

#### B. Pattern-Based Detection
- **Purpose**: Detect clear out-of-domain patterns
- **Categories**: Politics, weather, entertainment, technology, general knowledge, medical, shopping, personal
- **Confidence**: 0.8-0.9 per category
- **Weight**: 0.6 (configurable)

#### C. Semantic Similarity Analysis
- **Purpose**: Analyze vector similarity to knowledge base
- **Thresholds**: 
  - Very low (<0.2): 0.9 confidence out-of-domain
  - Low (<0.3): 0.7 confidence out-of-domain
- **Weight**: 0.4 (configurable)

#### D. Query Intent Classification
- **Purpose**: Classify the intent of the query
- **Intents**: Policy question, factual lookup, procedural, general chat, out-of-scope
- **Weight**: 0.5 (configurable)

### 2. Weighted Signal Combination

```javascript
totalScore = (domainKeywords * -0.3) + 
             (patterns * 0.6) + 
             (semantic * 0.4) + 
             (intent * 0.5)
```

Final decision: `isOutOfDomain = totalScore > 0.6`

### 3. Intelligent Response Generation

#### Category-Specific Responses
- **Politics**: "I can't provide information about political topics."
- **Weather**: "I don't have access to weather information."
- **Technology**: "I focus on HR policies rather than technical topics."
- **General**: Fallback message with HR policy guidance

#### Contextual Suggestions
- Analyze query content for potential redirects
- Generate alternative in-domain questions
- Provide specific examples of supported queries

### 4. Misunderstood Query Detection

Identifies queries that might be legitimate but misclassified:
- Contains HR-related terms but low confidence
- Provides alternative query suggestions
- Confidence threshold: 0.8

## Implementation Files

### Core Services
- `src/services/semanticValidationService.js` - Main detection logic
- `src/services/outOfDomainResponseService.js` - Response generation
- `src/config/outOfDomainConfig.js` - Configuration management

### Integration Points
- `src/middleware/guardrailsValidation.js` - Middleware integration
- `src/routes/index.js` - API endpoint integration

## Configuration

### Environment Variables
```bash
OUT_OF_DOMAIN_THRESHOLD=0.6
SEMANTIC_LOW_THRESHOLD=0.3
SEMANTIC_VERY_LOW_THRESHOLD=0.2
PATTERN_MATCH_CONFIDENCE=0.8
DOMAIN_KEYWORD_CONFIDENCE=0.8
MISUNDERSTOOD_THRESHOLD=0.8

# Weights
WEIGHT_DOMAIN_KEYWORDS=0.3
WEIGHT_PATTERNS=0.6
WEIGHT_SEMANTIC=0.4
WEIGHT_INTENT=0.5
```

### Customizable Patterns
The system supports adding new out-of-domain patterns:

```javascript
{
    pattern: /\b(new|pattern|keywords)\b/i,
    category: 'new_category',
    confidence: 0.9
}
```

## Performance Results

### Test Results (90.9% Accuracy)
- **Politics**: 100% accuracy
- **Weather**: 100% accuracy  
- **Entertainment**: 100% accuracy
- **Technology**: 100% accuracy
- **General Knowledge**: 100% accuracy
- **HR Policies**: 100% accuracy
- **Edge Cases**: 66.7% accuracy

### Example Detections

#### ✅ Correctly Detected Out-of-Domain
```
Query: "Who is the PM of India?"
Confidence: 0.970
Reasoning: No domain keywords | Politics pattern | Low semantic similarity
Response: "I can't provide information about political topics."
```

#### ✅ Correctly Detected In-Domain
```
Query: "What is the travel policy?"
Confidence: 0.000 (in-domain)
Reasoning: Domain keywords found: policy, travel | High semantic similarity
```

#### ⚠️ Edge Case
```
Query: "I need help with my personal taxes"
Confidence: 0.430 (below threshold, classified as in-domain)
Issue: "help" keyword triggered domain detection
Improvement: Add "personal taxes" to out-of-domain patterns
```

## API Integration

### Request Flow
1. **Semantic Pre-check**: Analyze query relevance
2. **Out-of-Domain Detection**: Multi-strategy analysis
3. **Response Generation**: Category-specific responses
4. **Guardrails Integration**: Validation result formatting

### Response Format
```javascript
{
    is_valid: false,
    validation_errors: [
        "I can't provide information about political topics.",
        "Ask about company policies and workplace guidelines"
    ],
    relevance_level: 'out-of-domain',
    out_of_domain: {
        isOutOfDomain: true,
        confidence: 0.970,
        reasoning: "...",
        matchedPatterns: ["politics"]
    }
}
```

## Monitoring and Tuning

### Key Metrics
- **Detection Accuracy**: Percentage of correctly classified queries
- **False Positives**: In-domain queries marked as out-of-domain
- **False Negatives**: Out-of-domain queries marked as in-domain
- **Response Quality**: User satisfaction with generated responses

### Tuning Guidelines
1. **Increase Threshold**: Reduce false positives (more permissive)
2. **Decrease Threshold**: Reduce false negatives (more restrictive)
3. **Adjust Weights**: Balance different detection strategies
4. **Add Patterns**: Handle new out-of-domain categories
5. **Update Keywords**: Expand domain-specific vocabulary

## Future Enhancements

1. **Machine Learning Integration**: Train models on query classification
2. **Dynamic Thresholds**: Adjust based on user feedback
3. **Multi-Language Support**: Extend patterns for other languages
4. **Context Awareness**: Consider conversation history
5. **A/B Testing**: Compare different threshold configurations

## Testing

Run the test suite:
```bash
node test-out-of-domain.js
```

Test individual strategies:
```bash
# Test semantic validation endpoint
curl -X POST http://localhost:3000/api/v1/chat/test-semantic \
  -H "Content-Type: application/json" \
  -d '{"query": "Who is the PM of India?", "appId": "test"}'
```

## Conclusion

The enhanced out-of-domain detection system provides:
- **High Accuracy**: 90.9% correct classification
- **Intelligent Responses**: Category-specific, helpful messages
- **Configurable**: Easily tunable thresholds and patterns
- **Extensible**: Support for new domains and patterns
- **User-Friendly**: Clear guidance for in-domain alternatives

This solution effectively addresses the original problem of queries like "PM of India" being misclassified, while maintaining flexibility for legitimate edge cases.
